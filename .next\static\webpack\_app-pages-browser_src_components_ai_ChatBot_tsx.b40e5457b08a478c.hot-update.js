"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_ai_ChatBot_tsx",{

/***/ "(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx":
/*!********************************************************************!*\
  !*** ./src/components/appointments/WeeklyAvailabilitySelector.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronLeft,ChevronRight,Clock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AvailabilityIndicator */ \"(app-pages-browser)/./src/components/appointments/AvailabilityIndicator.tsx\");\n/* harmony import */ var _components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ai/SmartSuggestionsDisplay */ \"(app-pages-browser)/./src/components/ai/SmartSuggestionsDisplay.tsx\");\n/* harmony import */ var _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai/SmartSuggestionsEngine */ \"(app-pages-browser)/./src/lib/ai/SmartSuggestionsEngine.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * WeeklyAvailabilitySelector Component\n * \n * Componente avanzado para selección de fechas con vista semanal,\n * indicadores de densidad de disponibilidad y navegación intuitiva.\n * Reemplaza al DateSelector tradicional en UnifiedAppointmentFlow.\n * \n * Características principales:\n * - Vista semanal con indicadores de densidad visual\n * - Navegación fluida entre semanas\n * - Colores semafóricos para disponibilidad (verde/amarillo/rojo/gris)\n * - Integración con sugerencias de IA\n * - Soporte para flujos Express y Personalized\n * - Arquitectura multi-tenant\n * \n * <AUTHOR> MVP Team - UX Enhancement Phase 1\n * @version 1.0.0\n */ \n\n\n\n\n/**\n * Hook para generar datos de disponibilidad semanal\n */ const useWeeklyAvailabilityData = (startDate, organizationId, serviceId, doctorId, locationId, onLoadAvailability)=>{\n    _s();\n    const [weekData, setWeekData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!onLoadAvailability) {\n            // Generar datos de ejemplo si no hay función de carga\n            const mockData = [];\n            const dayNames = [\n                \"Domingo\",\n                \"Lunes\",\n                \"Martes\",\n                \"Mi\\xe9rcoles\",\n                \"Jueves\",\n                \"Viernes\",\n                \"S\\xe1bado\"\n            ];\n            // DEBUG: Log inicial para investigar problema de fechas\n            console.log(\"=== DEBUG FECHA GENERACI\\xd3N ===\");\n            console.log(\"startDate original:\", startDate);\n            console.log(\"startDate ISO:\", startDate.toISOString());\n            console.log(\"startDate timezone offset:\", startDate.getTimezoneOffset());\n            for(let i = 0; i < 7; i++){\n                // CRITICAL FIX: Use timezone-safe date calculation\n                // Instead of setDate() which can cause timezone issues, use direct date arithmetic\n                const date = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate() + i);\n                // DEBUG: Log antes de cálculo\n                console.log(\"D\\xeda \".concat(i, \" (antes c\\xe1lculo):\"), {\n                    startDateYear: startDate.getFullYear(),\n                    startDateMonth: startDate.getMonth(),\n                    startDateDate: startDate.getDate(),\n                    indexI: i,\n                    calculation: startDate.getDate() + i\n                });\n                // DEBUG: Log después de cálculo timezone-safe\n                console.log(\"D\\xeda \".concat(i, \" (despu\\xe9s c\\xe1lculo timezone-safe):\"), {\n                    newDate: date.toISOString(),\n                    getDate: date.getDate(),\n                    getDay: date.getDay(),\n                    dayName: dayNames[date.getDay()],\n                    localDateString: \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"))\n                });\n                const today = new Date();\n                today.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                date.setHours(0, 0, 0, 0); // Normalizar a medianoche para comparación\n                const isToday = date.getTime() === today.getTime();\n                const isPastDate = date.getTime() < today.getTime();\n                const tomorrow = new Date(today);\n                tomorrow.setDate(today.getDate() + 1);\n                const isTomorrow = date.getTime() === tomorrow.getTime();\n                const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n                // CRITICAL FIX: Use timezone-safe date formatting (moved up to be available for logging)\n                const finalDateString = \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(date.getDate()).padStart(2, \"0\"));\n                // CRITICAL FIX: Apply 4-hour rule validation during weekData generation\n                // This ensures that dates that don't meet the 4-hour rule are marked as 'none' from the start\n                let slotsCount = 0;\n                let availabilityLevel = \"none\";\n                if (isPastDate) {\n                    availabilityLevel = \"none\"; // Fechas pasadas siempre sin disponibilidad\n                    slotsCount = 0;\n                } else {\n                    // Generate initial slots count\n                    const initialSlotsCount = isWeekend ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10);\n                    // CRITICAL FIX: Apply 4-hour advance booking rule validation\n                    // Check if this date would have any valid slots after applying the 4-hour rule\n                    const MINIMUM_ADVANCE_HOURS = 4;\n                    const MINIMUM_ADVANCE_MINUTES = MINIMUM_ADVANCE_HOURS * 60;\n                    const now = new Date();\n                    // Generate typical business hours for this date\n                    const businessHours = [\n                        \"08:00\",\n                        \"09:00\",\n                        \"10:00\",\n                        \"11:00\",\n                        \"14:00\",\n                        \"15:00\",\n                        \"16:00\",\n                        \"17:00\",\n                        \"18:00\"\n                    ];\n                    // Count how many slots would be valid after 4-hour rule\n                    let validSlotsCount = 0;\n                    if (initialSlotsCount > 0) {\n                        businessHours.forEach((timeSlot)=>{\n                            const [hours, minutes] = timeSlot.split(\":\").map(Number);\n                            const slotDateTime = new Date(date.getFullYear(), date.getMonth(), date.getDate(), hours, minutes);\n                            const timeDifferenceMs = slotDateTime.getTime() - now.getTime();\n                            const timeDifferenceMinutes = Math.floor(timeDifferenceMs / (1000 * 60));\n                            if (timeDifferenceMinutes >= MINIMUM_ADVANCE_MINUTES) {\n                                validSlotsCount++;\n                            }\n                        });\n                        // Only count slots that would actually be available after 4-hour rule\n                        slotsCount = Math.min(initialSlotsCount, validSlotsCount);\n                    }\n                    // Set availability level based on valid slots count\n                    if (slotsCount === 0) {\n                        availabilityLevel = \"none\";\n                    } else if (slotsCount <= 2) {\n                        availabilityLevel = \"low\";\n                    } else if (slotsCount <= 5) {\n                        availabilityLevel = \"medium\";\n                    } else {\n                        availabilityLevel = \"high\";\n                    }\n                    console.log(\"\\uD83D\\uDD0D VALIDACI\\xd3N 4H INTEGRADA - \".concat(finalDateString, \":\"), {\n                        initialSlotsCount,\n                        validSlotsCount,\n                        finalSlotsCount: slotsCount,\n                        availabilityLevel,\n                        isWeekend,\n                        timeDifferenceToFirstSlot: businessHours.length > 0 ? Math.floor((new Date(date.getFullYear(), date.getMonth(), date.getDate(), 8, 0).getTime() - now.getTime()) / (1000 * 60)) : \"N/A\"\n                    });\n                }\n                // DEBUG: Log datos finales con comparación\n                console.log(\"D\\xeda \".concat(i, \" (datos finales):\"), {\n                    date: finalDateString,\n                    dateISO: date.toISOString().split(\"T\")[0],\n                    dateLocal: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend,\n                    timezoneComparison: {\n                        iso: date.toISOString().split(\"T\")[0],\n                        local: finalDateString,\n                        match: date.toISOString().split(\"T\")[0] === finalDateString\n                    }\n                });\n                mockData.push({\n                    date: finalDateString,\n                    dayName: dayNames[date.getDay()],\n                    slotsCount,\n                    availabilityLevel,\n                    isToday,\n                    isTomorrow,\n                    isWeekend\n                });\n            }\n            console.log(\"=== DEBUG MOCK DATA FINAL ===\");\n            console.log(\"mockData completo:\", mockData);\n            console.log(\"================================\");\n            setWeekData(mockData);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const endDate = new Date(startDate);\n            endDate.setDate(startDate.getDate() + 6);\n            const data = await onLoadAvailability({\n                organizationId,\n                serviceId,\n                doctorId,\n                locationId,\n                startDate: startDate.toISOString().split(\"T\")[0],\n                endDate: endDate.toISOString().split(\"T\")[0]\n            });\n            // CRITICAL FIX: Apply 4-hour validation to real API data\n            // The API returns data with availabilityLevel, but we need to validate it against 4-hour rule\n            console.log(\"=== APLICANDO VALIDACI\\xd3N 4H A DATOS REALES ===\");\n            console.log(\"Datos originales del API:\", data);\n            const processedData = data.map((day)=>{\n                console.log(\"\\uD83D\\uDD0D VALIDACI\\xd3N 4H REAL - \".concat(day.date, \":\"), {\n                    originalAvailabilityLevel: day.availabilityLevel,\n                    originalSlotsCount: day.slotsCount\n                });\n                // Apply 4-hour rule validation to real data\n                const MINIMUM_ADVANCE_HOURS = 4;\n                const MINIMUM_ADVANCE_MINUTES = MINIMUM_ADVANCE_HOURS * 60;\n                const now = new Date();\n                // Parse date components safely\n                const [year, month, dayNum] = day.date.split(\"-\").map(Number);\n                const dateObj = new Date(year, month - 1, dayNum);\n                // Check if it's a past date\n                const today = new Date();\n                today.setHours(0, 0, 0, 0);\n                dateObj.setHours(0, 0, 0, 0);\n                const isPastDate = dateObj.getTime() < today.getTime();\n                if (isPastDate) {\n                    console.log(\"\\uD83D\\uDCC5 \".concat(day.date, \": FECHA PASADA - forzando availabilityLevel = 'none'\"));\n                    return {\n                        ...day,\n                        availabilityLevel: \"none\",\n                        slotsCount: 0\n                    };\n                }\n                // For current and future dates, check 4-hour rule\n                if (day.availabilityLevel !== \"none\" && day.slotsCount > 0) {\n                    // Generate typical business hours to validate against 4-hour rule\n                    const businessHours = [\n                        \"08:00\",\n                        \"09:00\",\n                        \"10:00\",\n                        \"11:00\",\n                        \"14:00\",\n                        \"15:00\",\n                        \"16:00\",\n                        \"17:00\",\n                        \"18:00\"\n                    ];\n                    let validSlotsCount = 0;\n                    businessHours.forEach((timeSlot)=>{\n                        const [hours, minutes] = timeSlot.split(\":\").map(Number);\n                        const slotDateTime = new Date(year, month - 1, dayNum, hours, minutes);\n                        const timeDifferenceMs = slotDateTime.getTime() - now.getTime();\n                        const timeDifferenceMinutes = Math.floor(timeDifferenceMs / (1000 * 60));\n                        if (timeDifferenceMinutes >= MINIMUM_ADVANCE_MINUTES) {\n                            validSlotsCount++;\n                        }\n                    });\n                    // If no slots meet the 4-hour rule, override the API data\n                    if (validSlotsCount === 0) {\n                        console.log(\"\\uD83D\\uDCC5 \".concat(day.date, \": SIN SLOTS V\\xc1LIDOS (4H) - forzando availabilityLevel = 'none'\"));\n                        return {\n                            ...day,\n                            availabilityLevel: \"none\",\n                            slotsCount: 0\n                        };\n                    } else {\n                        // Adjust slots count based on valid slots\n                        const adjustedSlotsCount = Math.min(day.slotsCount, validSlotsCount);\n                        let adjustedAvailabilityLevel = day.availabilityLevel;\n                        // Recalculate availability level based on valid slots\n                        if (adjustedSlotsCount <= 2) {\n                            adjustedAvailabilityLevel = \"low\";\n                        } else if (adjustedSlotsCount <= 5) {\n                            adjustedAvailabilityLevel = \"medium\";\n                        } else {\n                            adjustedAvailabilityLevel = \"high\";\n                        }\n                        console.log(\"\\uD83D\\uDCC5 \".concat(day.date, \": SLOTS AJUSTADOS (4H) - original: \").concat(day.slotsCount, \", v\\xe1lidos: \").concat(validSlotsCount, \", final: \").concat(adjustedSlotsCount, \", level: \").concat(adjustedAvailabilityLevel));\n                        return {\n                            ...day,\n                            availabilityLevel: adjustedAvailabilityLevel,\n                            slotsCount: adjustedSlotsCount\n                        };\n                    }\n                }\n                // If already 'none' or 0 slots, keep as is\n                console.log(\"\\uD83D\\uDCC5 \".concat(day.date, \": SIN CAMBIOS - ya era 'none' o 0 slots\"));\n                return day;\n            });\n            console.log(\"Datos procesados con validaci\\xf3n 4H:\", processedData);\n            console.log(\"===============================================\");\n            setWeekData(processedData);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Error cargando disponibilidad\");\n            console.error(\"Error loading availability data:\", err);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        startDate,\n        organizationId,\n        serviceId,\n        doctorId,\n        locationId,\n        onLoadAvailability\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadWeekData();\n    }, [\n        loadWeekData\n    ]);\n    return {\n        weekData,\n        loading,\n        error,\n        refetch: loadWeekData\n    };\n};\n_s(useWeeklyAvailabilityData, \"ptLZ7BmszhXAGSPmNKpF1scaotk=\");\n/**\n * Componente principal WeeklyAvailabilitySelector\n */ const WeeklyAvailabilitySelector = (param)=>{\n    let { title, subtitle, selectedDate, onDateSelect, organizationId, serviceId, doctorId, locationId, minDate, showDensityIndicators = true, enableSmartSuggestions = false, aiContext, entryMode = \"manual\", compactSuggestions = false, onLoadAvailability, loading: externalLoading = false, className = \"\" } = param;\n    _s1();\n    const [currentWeek, setCurrentWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - today.getDay()); // Domingo como inicio de semana\n        return startOfWeek;\n    });\n    const { weekData, loading: dataLoading, error, refetch } = useWeeklyAvailabilityData(currentWeek, organizationId, serviceId, doctorId, locationId, onLoadAvailability);\n    // Smart Suggestions state\n    const [smartSuggestions, setSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSuggestions, setLoadingSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSmartSuggestions, setShowSmartSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isLoading = externalLoading || dataLoading;\n    /**\n   * CRITICAL FEATURE: UI-level date blocking validation\n   * SIMPLIFIED: Now that 4-hour rule is integrated into weekData generation,\n   * we primarily use availabilityLevel to determine blocking state\n   */ const dateValidationResults = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (weekData.length === 0) return {};\n        console.log(\"=== DEBUG DATE BLOCKING VALIDATION (SIMPLIFIED) ===\");\n        const validationResults = {};\n        weekData.forEach((day)=>{\n            const isBlocked = day.availabilityLevel === \"none\";\n            // CRITICAL FIX: Proper date comparison logic to distinguish between past dates and 4-hour rule blocks\n            let reason = undefined;\n            if (isBlocked) {\n                // Parse date components safely to avoid timezone issues\n                const [year, month, dayNum] = day.date.split(\"-\").map(Number);\n                const dayDateObj = new Date(year, month - 1, dayNum);\n                // Get today's date normalized to midnight for comparison\n                const today = new Date();\n                today.setHours(0, 0, 0, 0);\n                dayDateObj.setHours(0, 0, 0, 0);\n                // Check if it's actually a past date\n                const isPastDate = dayDateObj.getTime() < today.getTime();\n                if (isPastDate) {\n                    reason = \"Fecha pasada - No se pueden agendar citas en fechas anteriores\";\n                    console.log(\"\\uD83D\\uDD0D REASON LOGIC - \".concat(day.date, \": FECHA PASADA detectada (\").concat(dayDateObj.toDateString(), \" < \").concat(today.toDateString(), \")\"));\n                } else {\n                    // It's a current or future date blocked by 4-hour rule\n                    reason = \"Reserva con m\\xednimo 4 horas de anticipaci\\xf3n requerida\";\n                    console.log(\"\\uD83D\\uDD0D REASON LOGIC - \".concat(day.date, \": REGLA 4H detectada (\").concat(dayDateObj.toDateString(), \" >= \").concat(today.toDateString(), \")\"));\n                }\n            }\n            validationResults[day.date] = {\n                isValid: !isBlocked,\n                reason\n            };\n            console.log(\"\\uD83D\\uDCC5 \".concat(day.date, \": availabilityLevel=\").concat(day.availabilityLevel, \", isBlocked=\").concat(isBlocked, ', reason=\"').concat(reason, '\"'));\n        });\n        console.log(\"Simplified validation results:\", validationResults);\n        console.log(\"=============================================\");\n        return validationResults;\n    }, [\n        weekData\n    ]);\n    /**\n   * Enhanced week data with blocking information\n   */ const enhancedWeekData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return weekData.map((day)=>{\n            const validation = dateValidationResults[day.date];\n            const isBlocked = validation && !validation.isValid;\n            return {\n                ...day,\n                isBlocked,\n                blockReason: validation === null || validation === void 0 ? void 0 : validation.reason,\n                validationResult: validation\n            };\n        });\n    }, [\n        weekData,\n        dateValidationResults\n    ]);\n    /**\n   * Navegar entre semanas\n   */ const navigateWeek = (direction)=>{\n        // DEBUG: Log navegación semanal\n        console.log(\"=== DEBUG NAVEGACI\\xd3N SEMANAL ===\");\n        console.log(\"Direcci\\xf3n:\", direction);\n        console.log(\"currentWeek actual:\", currentWeek);\n        console.log(\"currentWeek ISO:\", currentWeek.toISOString());\n        console.log(\"minDate prop:\", minDate);\n        const newWeek = new Date(currentWeek);\n        newWeek.setDate(currentWeek.getDate() + (direction === \"next\" ? 7 : -7));\n        console.log(\"newWeek calculada:\", newWeek);\n        console.log(\"newWeek ISO:\", newWeek.toISOString());\n        // Validar fecha mínima\n        if (minDate && direction === \"prev\") {\n            const minDateObj = new Date(minDate);\n            console.log(\"minDateObj:\", minDateObj);\n            console.log(\"Comparaci\\xf3n newWeek < minDateObj:\", newWeek < minDateObj);\n            if (newWeek < minDateObj) {\n                console.log(\"BLOQUEADO por minDate - no se permite navegar antes de fecha m\\xednima\");\n                console.log(\"================================\");\n                return; // No permitir navegar antes de la fecha mínima\n            }\n        }\n        // Validar que no se navegue a semanas completamente en el pasado\n        if (direction === \"prev\") {\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            console.log(\"today normalizado:\", today);\n            // Calcular el último día de la nueva semana\n            const endOfNewWeek = new Date(newWeek);\n            endOfNewWeek.setDate(newWeek.getDate() + 6);\n            endOfNewWeek.setHours(0, 0, 0, 0);\n            console.log(\"endOfNewWeek:\", endOfNewWeek);\n            console.log(\"Comparaci\\xf3n endOfNewWeek < today:\", endOfNewWeek.getTime() < today.getTime());\n            // Si toda la semana está en el pasado, no permitir navegación\n            if (endOfNewWeek.getTime() < today.getTime()) {\n                console.log(\"BLOQUEADO por semana en el pasado\");\n                console.log(\"================================\");\n                return;\n            }\n        }\n        console.log(\"NAVEGACI\\xd3N PERMITIDA - actualizando currentWeek\");\n        console.log(\"================================\");\n        setCurrentWeek(newWeek);\n    };\n    /**\n   * Formatear rango de semana\n   */ const formatWeekRange = (startDate)=>{\n        const endDate = new Date(startDate);\n        endDate.setDate(startDate.getDate() + 6);\n        const startDay = startDate.getDate();\n        const endDay = endDate.getDate();\n        const month = startDate.toLocaleDateString(\"es-ES\", {\n            month: \"long\"\n        });\n        const year = startDate.getFullYear();\n        return \"\".concat(startDay, \"-\").concat(endDay, \" \").concat(month, \" \").concat(year);\n    };\n    /**\n   * Manejar selección de fecha con validación de bloqueo\n   */ const handleDateSelect = (date)=>{\n        // DEBUG: Log selección de fecha con análisis timezone\n        console.log(\"=== DEBUG SELECCI\\xd3N FECHA (TIMEZONE-SAFE + BLOCKING) ===\");\n        console.log(\"Fecha seleccionada (string):\", date);\n        // CRITICAL FEATURE: Check if date is blocked by UI validation\n        const validation = dateValidationResults[date];\n        const isBlocked = validation && !validation.isValid;\n        console.log(\"Validaci\\xf3n de bloqueo:\");\n        console.log(\"  - validation:\", validation);\n        console.log(\"  - isBlocked:\", isBlocked);\n        console.log(\"  - blockReason:\", validation === null || validation === void 0 ? void 0 : validation.reason);\n        if (isBlocked) {\n            console.log(\"\\uD83D\\uDEAB FECHA BLOQUEADA - No se permite selecci\\xf3n\");\n            console.log(\"Raz\\xf3n:\", validation === null || validation === void 0 ? void 0 : validation.reason);\n            console.log(\"=======================================\");\n            // Show user feedback (could be enhanced with toast notification)\n            alert(\"Esta fecha no est\\xe1 disponible: \".concat(validation === null || validation === void 0 ? void 0 : validation.reason));\n            return;\n        }\n        // CRITICAL FIX: Create timezone-safe Date object\n        // Problem: new Date(\"2025-05-29\") creates May 28 in GMT-0500\n        // Solution: Parse date components manually to avoid UTC interpretation\n        const [year, month, day] = date.split(\"-\").map(Number);\n        const dateObj = new Date(year, month - 1, day); // month is 0-indexed\n        const localDateString = \"\".concat(dateObj.getFullYear(), \"-\").concat(String(dateObj.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(dateObj.getDate()).padStart(2, \"0\"));\n        // Also create UTC version for comparison\n        const dateObjUTC = new Date(date); // This creates the problematic UTC interpretation\n        console.log(\"Date object creado (timezone-safe):\", dateObj);\n        console.log(\"Date object creado (UTC interpretation):\", dateObjUTC);\n        console.log(\"Date object ISO (UTC):\", dateObjUTC.toISOString());\n        console.log(\"Date object local string (timezone-safe):\", localDateString);\n        console.log(\"Timezone offset (minutes):\", dateObj.getTimezoneOffset());\n        // CRITICAL FIX: Correct timezone desfase detection logic using timezone-safe objects\n        const utcDateStringFromUTC = dateObjUTC.toISOString().split(\"T\")[0];\n        const utcDateStringFromLocal = dateObj.toISOString().split(\"T\")[0];\n        const hasTimezoneDesfase = date !== utcDateStringFromUTC;\n        console.log(\"Comparaci\\xf3n timezone (CORREGIDA):\");\n        console.log(\"  - date (input):\", date);\n        console.log(\"  - utcDateString (from UTC obj):\", utcDateStringFromUTC);\n        console.log(\"  - utcDateString (from local obj):\", utcDateStringFromLocal);\n        console.log(\"  - localDateString (timezone-safe):\", localDateString);\n        console.log(\"\\xbfHay desfase timezone?:\", hasTimezoneDesfase);\n        console.log(\"\\xbfDate objects son consistentes?:\", localDateString === date);\n        console.log(\"minDate:\", minDate);\n        console.log(\"Comparaci\\xf3n date < minDate:\", date < minDate);\n        // Validar fecha mínima\n        if (minDate && date < minDate) {\n            console.log(\"BLOQUEADO por minDate\");\n            console.log(\"=======================================\");\n            return;\n        }\n        console.log(\"✅ FECHA V\\xc1LIDA - LLAMANDO onDateSelect con fecha timezone-safe:\", date);\n        onDateSelect(date);\n        console.log(\"=======================================\");\n    };\n    /**\n   * Generar sugerencias inteligentes usando SmartSuggestionsEngine\n   */ const generateSmartSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!enableSmartSuggestions || !aiContext || weekData.length === 0) {\n            return;\n        }\n        setLoadingSuggestions(true);\n        try {\n            const suggestionsEngine = new _lib_ai_SmartSuggestionsEngine__WEBPACK_IMPORTED_MODULE_4__.SmartSuggestionsEngine(organizationId);\n            // Convertir weekData a formato de opciones disponibles\n            const availableOptions = weekData.filter((day)=>day.availabilityLevel !== \"none\").flatMap((day)=>{\n                var _day_slots;\n                return ((_day_slots = day.slots) === null || _day_slots === void 0 ? void 0 : _day_slots.map((slot)=>({\n                        date: day.date,\n                        time: slot.time,\n                        doctorId: slot.doctorId,\n                        doctorName: slot.doctorName,\n                        available: slot.available,\n                        price: slot.price\n                    }))) || [\n                    {\n                        date: day.date,\n                        time: \"09:00\",\n                        available: true\n                    }\n                ];\n            });\n            if (availableOptions.length === 0) {\n                setSmartSuggestions(null);\n                return;\n            }\n            const result = await suggestionsEngine.generateSuggestions(aiContext, availableOptions);\n            setSmartSuggestions(result);\n            setShowSmartSuggestions(true);\n        } catch (error) {\n            console.error(\"Error generating smart suggestions:\", error);\n            setSmartSuggestions(null);\n        } finally{\n            setLoadingSuggestions(false);\n        }\n    }, [\n        enableSmartSuggestions,\n        aiContext,\n        weekData,\n        organizationId\n    ]);\n    /**\n   * Obtener sugerencias de IA básicas (fallback)\n   */ const getAISuggestions = ()=>{\n        if (!enableSmartSuggestions || !(aiContext === null || aiContext === void 0 ? void 0 : aiContext.suggestedDates)) {\n            return [];\n        }\n        return weekData.filter((day)=>{\n            var _aiContext_suggestedDates;\n            return ((_aiContext_suggestedDates = aiContext.suggestedDates) === null || _aiContext_suggestedDates === void 0 ? void 0 : _aiContext_suggestedDates.includes(day.date)) && day.availabilityLevel !== \"none\";\n        });\n    };\n    const aiSuggestions = getAISuggestions();\n    // Generar sugerencias inteligentes cuando cambian los datos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (enableSmartSuggestions && aiContext && weekData.length > 0) {\n            generateSmartSuggestions();\n        }\n    }, [\n        generateSmartSuggestions\n    ]);\n    /**\n   * Manejar selección de sugerencia inteligente\n   */ const handleSmartSuggestionSelect = (suggestion)=>{\n        if (suggestion.data.date) {\n            handleDateSelect(suggestion.data.date);\n        }\n        setShowSmartSuggestions(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5 mr-2 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 745,\n                                columnNumber: 11\n                            }, undefined),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 744,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 748,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 743,\n                columnNumber: 7\n            }, undefined),\n            enableSmartSuggestions && showSmartSuggestions && smartSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_SmartSuggestionsDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    suggestionsResult: smartSuggestions,\n                    onSuggestionSelect: handleSmartSuggestionSelect,\n                    loading: loadingSuggestions,\n                    showMetrics: false,\n                    compact: compactSuggestions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 754,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 753,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && !showSmartSuggestions && aiSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 768,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Sugerencias inteligentes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 767,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: aiSuggestions.slice(0, 3).map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>handleDateSelect(suggestion.date),\n                                className: \"p-3 bg-white rounded-lg border-2 border-purple-200 hover:border-purple-400 transition-colors text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: suggestion.isToday ? \"Hoy\" : suggestion.isTomorrow ? \"Ma\\xf1ana\" : suggestion.dayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: suggestion.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-600 mt-1\",\n                                        children: [\n                                            index === 0 && \"⭐ Recomendado\",\n                                            index === 1 && \"\\uD83D\\uDD50 Flexible\",\n                                            index === 2 && \"\\uD83D\\uDE80 Pr\\xf3ximo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, suggestion.date, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 773,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 771,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 766,\n                columnNumber: 9\n            }, undefined),\n            enableSmartSuggestions && loadingSuggestions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-purple-800 font-medium\",\n                            children: \"Generando sugerencias inteligentes...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                            lineNumber: 799,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 797,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 796,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"prev\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 812,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Anterior\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 806,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: formatWeekRange(currentWeek)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 816,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        disabled: isLoading,\n                        className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: [\n                            \"Siguiente\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 827,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 820,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 805,\n                columnNumber: 7\n            }, undefined),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 834,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando disponibilidad...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 835,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 833,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-red-600 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 font-medium\",\n                                children: \"Error cargando disponibilidad\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 842,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-sm mt-1\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 846,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: refetch,\n                        className: \"mt-2 text-sm text-red-600 hover:text-red-700 underline\",\n                        children: \"Intentar de nuevo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 847,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 841,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && enhancedWeekData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AvailabilityIndicator__WEBPACK_IMPORTED_MODULE_2__.WeeklyAvailability, {\n                    weekData: enhancedWeekData.map((day)=>({\n                            date: day.date,\n                            dayName: day.dayName,\n                            slotsCount: day.slotsCount,\n                            isBlocked: day.isBlocked,\n                            blockReason: day.blockReason\n                        })),\n                    selectedDate: selectedDate,\n                    onDateSelect: handleDateSelect,\n                    size: \"lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                    lineNumber: 860,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 859,\n                columnNumber: 9\n            }, undefined),\n            showDensityIndicators && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: \"Disponibilidad:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 878,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 881,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Alta (6+ slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 882,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 880,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-yellow-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 885,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Media (3-5 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 886,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 884,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 889,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Baja (1-2 slots)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 890,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 888,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-gray-400 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 893,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"No disponible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                                lineNumber: 892,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 879,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 877,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && !error && weekData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronLeft_ChevronRight_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 903,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Sin disponibilidad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 904,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"No hay horarios disponibles para esta semana.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 905,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>navigateWeek(\"next\"),\n                        className: \"mt-4 text-blue-600 hover:text-blue-700 font-medium\",\n                        children: \"Ver pr\\xf3xima semana\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                        lineNumber: 906,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n                lineNumber: 902,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\components\\\\appointments\\\\WeeklyAvailabilitySelector.tsx\",\n        lineNumber: 741,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeeklyAvailabilitySelector, \"14U+tOJsF/03vp4I7+MnLlHkFdQ=\", false, function() {\n    return [\n        useWeeklyAvailabilityData\n    ];\n});\n_c = WeeklyAvailabilitySelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeeklyAvailabilitySelector);\nvar _c;\n$RefreshReg$(_c, \"WeeklyAvailabilitySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/appointments/WeeklyAvailabilitySelector.tsx\n"));

/***/ })

});