"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/doctors/route";
exports.ids = ["app/api/doctors/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdoctors%2Froute&page=%2Fapi%2Fdoctors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdoctors%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdoctors%2Froute&page=%2Fapi%2Fdoctors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdoctors%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Juan_Pulgarin_Documents_augment_projects_agensalud_sonnet4_src_app_api_doctors_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/doctors/route.ts */ \"(rsc)/./src/app/api/doctors/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/doctors/route\",\n        pathname: \"/api/doctors\",\n        filename: \"route\",\n        bundlePath: \"app/api/doctors/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\api\\\\doctors\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Juan_Pulgarin_Documents_augment_projects_agensalud_sonnet4_src_app_api_doctors_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/doctors/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdoctors%2Froute&page=%2Fapi%2Fdoctors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdoctors%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/doctors/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/doctors/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_supabase_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase/service */ \"(rsc)/./src/lib/supabase/service.ts\");\n\n\n\n/**\n * GET /api/doctors\n * Get doctors for an organization, optionally filtered by service\n */ async function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const organizationId = searchParams.get(\"organizationId\");\n        const serviceId = searchParams.get(\"serviceId\");\n        if (!organizationId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Organization ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const serviceSupabase = (0,_lib_supabase_service__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n        // Get current user and verify permissions\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        let doctors;\n        if (serviceId) {\n            // First get doctor profile IDs who can provide the specific service\n            // Use service role client to bypass RLS for this internal query\n            const { data: doctorServices, error: serviceError } = await serviceSupabase.from(\"doctor_services\").select(\"doctor_id\").eq(\"service_id\", serviceId);\n            console.log(`DEBUG: Doctor services query - Service: ${serviceId}, Results: ${doctorServices?.length || 0}, Error: ${serviceError?.message || \"none\"}`);\n            if (serviceError) {\n                console.error(\"Error fetching doctor services:\", serviceError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Failed to fetch doctor services\"\n                }, {\n                    status: 500\n                });\n            }\n            const doctorProfileIds = doctorServices?.map((ds)=>ds.doctor_id) || [];\n            console.log(`DEBUG: Doctor profile IDs: ${JSON.stringify(doctorProfileIds)}`);\n            if (doctorProfileIds.length === 0) {\n                console.log(`DEBUG: No doctor profile IDs found for service ${serviceId}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: []\n                });\n            }\n            // Filter doctors who can provide the specific service\n            // Use service role client to bypass RLS for profile access\n            const { data: filteredDoctors, error } = await serviceSupabase.from(\"doctors\").select(`\n          id,\n          specialization,\n          consultation_fee,\n          is_available,\n          profiles(\n            id,\n            first_name,\n            last_name,\n            email\n          )\n        `).eq(\"organization_id\", organizationId).eq(\"is_available\", true).in(\"profile_id\", doctorProfileIds);\n            console.log(`DEBUG: Doctors query - Org: ${organizationId}, Profile IDs: ${doctorProfileIds.length}, Results: ${filteredDoctors?.length || 0}, Error: ${error?.message || \"none\"}`);\n            if (filteredDoctors && filteredDoctors.length > 0) {\n                console.log(`DEBUG: Sample doctor data:`, JSON.stringify(filteredDoctors[0], null, 2));\n            }\n            doctors = {\n                data: filteredDoctors,\n                error\n            };\n        } else {\n            // Return all available doctors\n            // Use service role client to bypass RLS for profile access\n            doctors = await serviceSupabase.from(\"doctors\").select(`\n          id,\n          specialization,\n          consultation_fee,\n          is_available,\n          profiles(\n            id,\n            first_name,\n            last_name,\n            email\n          )\n        `).eq(\"organization_id\", organizationId).eq(\"is_available\", true);\n        }\n        const { data: doctorsData, error } = doctors;\n        if (error) {\n            console.error(\"Error fetching doctors:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch doctors\"\n            }, {\n                status: 500\n            });\n        }\n        const filteredDoctors = doctorsData?.filter((doctor)=>doctor.profiles) || [];\n        const mappedDoctors = filteredDoctors.map((doctor)=>({\n                id: doctor.id,\n                name: `${doctor.profiles.first_name} ${doctor.profiles.last_name}`,\n                specialization: doctor.specialization,\n                consultation_fee: doctor.consultation_fee,\n                is_available: doctor.is_available,\n                profiles: doctor.profiles\n            }));\n        console.log(`DEBUG: API Response - Service: ${serviceId || \"ALL\"}, Doctors found: ${mappedDoctors.length}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: mappedDoctors\n        });\n    } catch (error) {\n        console.error(\"Doctors API Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/doctors/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://fjvletqwwmxusgthwphr.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZqdmxldHF3d214dXNndGh3cGhyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDc2MDAsImV4cCI6MjA2Mzc4MzYwMH0.TiU8DGo9kihikfmlk1drLs57tNuOrm_Pgq80yzsWytc\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set(name, value, options);\n                } catch  {\n                // The `set` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            },\n            remove (name, options) {\n                try {\n                    cookieStore.set(name, \"\", {\n                        ...options,\n                        maxAge: 0\n                    });\n                } catch  {\n                // The `remove` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/service.ts":
/*!*************************************!*\
  !*** ./src/lib/supabase/service.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n/**\n * Create a Supabase client with service role key for server-side operations\n * This client bypasses RLS policies and should only be used for trusted operations\n */ function createClient() {\n    const supabaseUrl = \"https://fjvletqwwmxusgthwphr.supabase.co\";\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseUrl || !supabaseServiceKey) {\n        throw new Error(\"Missing Supabase environment variables for service client\");\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkU7QUFHN0U7OztDQUdDLEdBQ00sU0FBU0E7SUFDZCxNQUFNRSxjQUFjQywwQ0FBb0M7SUFDeEQsTUFBTUcscUJBQXFCSCxRQUFRQyxHQUFHLENBQUNHLHlCQUF5QjtJQUVoRSxJQUFJLENBQUNMLGVBQWUsQ0FBQ0ksb0JBQW9CO1FBQ3ZDLE1BQU0sSUFBSUUsTUFBTTtJQUNsQjtJQUVBLE9BQU9QLG1FQUFvQkEsQ0FBV0MsYUFBYUksb0JBQW9CO1FBQ3JFRyxNQUFNO1lBQ0pDLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2FnZW5kYWxvLy4vc3JjL2xpYi9zdXBhYmFzZS9zZXJ2aWNlLnRzP2MyMGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IGFzIGNyZWF0ZVN1cGFiYXNlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcbmltcG9ydCB0eXBlIHsgRGF0YWJhc2UgfSBmcm9tICdAL3R5cGVzL2RhdGFiYXNlJztcblxuLyoqXG4gKiBDcmVhdGUgYSBTdXBhYmFzZSBjbGllbnQgd2l0aCBzZXJ2aWNlIHJvbGUga2V5IGZvciBzZXJ2ZXItc2lkZSBvcGVyYXRpb25zXG4gKiBUaGlzIGNsaWVudCBieXBhc3NlcyBSTFMgcG9saWNpZXMgYW5kIHNob3VsZCBvbmx5IGJlIHVzZWQgZm9yIHRydXN0ZWQgb3BlcmF0aW9uc1xuICovXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ2xpZW50KCkge1xuICBjb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTDtcbiAgY29uc3Qgc3VwYWJhc2VTZXJ2aWNlS2V5ID0gcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWTtcblxuICBpZiAoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZVNlcnZpY2VLZXkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ01pc3NpbmcgU3VwYWJhc2UgZW52aXJvbm1lbnQgdmFyaWFibGVzIGZvciBzZXJ2aWNlIGNsaWVudCcpO1xuICB9XG5cbiAgcmV0dXJuIGNyZWF0ZVN1cGFiYXNlQ2xpZW50PERhdGFiYXNlPihzdXBhYmFzZVVybCwgc3VwYWJhc2VTZXJ2aWNlS2V5LCB7XG4gICAgYXV0aDoge1xuICAgICAgYXV0b1JlZnJlc2hUb2tlbjogZmFsc2UsXG4gICAgICBwZXJzaXN0U2Vzc2lvbjogZmFsc2VcbiAgICB9XG4gIH0pO1xufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsImNyZWF0ZVN1cGFiYXNlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VTZXJ2aWNlS2V5IiwiU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSIsIkVycm9yIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/service.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/@opentelemetry","vendor-chunks/whatwg-url","vendor-chunks/ramda","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdoctors%2Froute&page=%2Fapi%2Fdoctors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdoctors%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();