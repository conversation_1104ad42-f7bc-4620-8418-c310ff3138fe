"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/doctors/availability/route";
exports.ids = ["app/api/doctors/availability/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdoctors%2Favailability%2Froute&page=%2Fapi%2Fdoctors%2Favailability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdoctors%2Favailability%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdoctors%2Favailability%2Froute&page=%2Fapi%2Fdoctors%2Favailability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdoctors%2Favailability%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Juan_Pulgarin_Documents_augment_projects_agensalud_sonnet4_src_app_api_doctors_availability_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/doctors/availability/route.ts */ \"(rsc)/./src/app/api/doctors/availability/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/doctors/availability/route\",\n        pathname: \"/api/doctors/availability\",\n        filename: \"route\",\n        bundlePath: \"app/api/doctors/availability/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agensalud-sonnet4\\\\src\\\\app\\\\api\\\\doctors\\\\availability\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Juan_Pulgarin_Documents_augment_projects_agensalud_sonnet4_src_app_api_doctors_availability_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/doctors/availability/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdoctors%2Favailability%2Froute&page=%2Fapi%2Fdoctors%2Favailability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdoctors%2Favailability%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/doctors/availability/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/doctors/availability/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/**\n * API Route for Doctor Availability\n * Provides available time slots for appointment booking\n *\n * @route GET /api/doctors/availability - Get available time slots\n */ \n\n\n// Validation schema for availability query\nconst availabilityQuerySchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    organizationId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().uuid(),\n    serviceId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().uuid().optional(),\n    doctorId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().uuid().optional(),\n    date: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().regex(/^\\d{4}-\\d{2}-\\d{2}$/),\n    duration: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().transform(Number).pipe(zod__WEBPACK_IMPORTED_MODULE_2__.z.number().min(15).max(240)).default(\"30\")\n});\n// GET /api/doctors/availability\nasync function GET(request) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { searchParams } = new URL(request.url);\n        // Extract and validate query parameters\n        const queryParams = {\n            organizationId: searchParams.get(\"organizationId\"),\n            serviceId: searchParams.get(\"serviceId\") || undefined,\n            doctorId: searchParams.get(\"doctorId\") || undefined,\n            date: searchParams.get(\"date\"),\n            duration: searchParams.get(\"duration\") || \"30\"\n        };\n        const validationResult = availabilityQuerySchema.safeParse(queryParams);\n        if (!validationResult.success) {\n            console.error(\"Validation failed for availability query:\", {\n                queryParams,\n                errors: validationResult.error.errors\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid query parameters\",\n                details: validationResult.error.errors,\n                received: queryParams\n            }, {\n                status: 400\n            });\n        }\n        const { organizationId, serviceId, doctorId, date, duration } = validationResult.data;\n        // Parse date and get day of week\n        const targetDate = new Date(date);\n        const dayOfWeek = targetDate.getDay(); // 0 = Sunday, 6 = Saturday\n        // Use a simplified approach - get all doctors first, then filter\n        console.log(\"Fetching doctors for organization:\", organizationId, \"day:\", dayOfWeek);\n        // Check authentication first\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        console.log(\"DEBUG: Auth user:\", user?.id || \"No user\", \"Auth error:\", authError?.message || \"None\");\n        // Use service role client for public availability data\n        const { createClient: createServiceClient } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_supabase_service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/service */ \"(rsc)/./src/lib/supabase/service.ts\"));\n        const serviceSupabase = createServiceClient();\n        let allDoctors;\n        if (serviceId) {\n            // First get doctor IDs who can provide the specific service\n            const { data: doctorServices, error: serviceError } = await serviceSupabase.from(\"doctor_services\").select(\"doctor_id\").eq(\"service_id\", serviceId);\n            if (serviceError) {\n                console.error(\"Error fetching doctor services:\", serviceError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Failed to fetch doctor services\"\n                }, {\n                    status: 500\n                });\n            }\n            const doctorIds = doctorServices?.map((ds)=>ds.doctor_id) || [];\n            if (doctorIds.length === 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    slots: [],\n                    message: \"No doctors available for this service\"\n                });\n            }\n            // Filter doctors who can provide the specific service\n            // CRITICAL FIX: doctorIds contains profile_id values from doctor_services table\n            // We need to search by profile_id, not id\n            const { data: filteredDoctors, error: doctorsError } = await serviceSupabase.from(\"doctors\").select(`\n          id,\n          profile_id,\n          specialization,\n          consultation_fee,\n          profiles(id, first_name, last_name, email)\n        `).eq(\"organization_id\", organizationId).eq(\"is_available\", true).in(\"profile_id\", doctorIds);\n            allDoctors = {\n                data: filteredDoctors,\n                error: doctorsError\n            };\n        } else {\n            // Return all available doctors\n            const { data: allDoctorsData, error: doctorsError } = await serviceSupabase.from(\"doctors\").select(`\n          id,\n          profile_id,\n          specialization,\n          consultation_fee,\n          profiles(id, first_name, last_name, email)\n        `).eq(\"organization_id\", organizationId).eq(\"is_available\", true);\n            allDoctors = {\n                data: allDoctorsData,\n                error: doctorsError\n            };\n        }\n        const { data: doctorsData, error: doctorsError } = allDoctors;\n        if (doctorsError) {\n            console.error(\"Error fetching doctors:\", doctorsError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch doctor availability\"\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"DEBUG: All doctors fetched:\", doctorsData?.length || 0);\n        // Filter by specific doctor if doctorId is provided\n        let filteredDoctorsData = doctorsData;\n        if (doctorId && doctorsData) {\n            filteredDoctorsData = doctorsData.filter((doctor)=>doctor.id === doctorId);\n            console.log(\"DEBUG: Filtered to specific doctor:\", doctorId, \"Found:\", filteredDoctorsData.length);\n            if (filteredDoctorsData.length === 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: [],\n                    message: \"Specified doctor not found or not available\"\n                });\n            }\n        }\n        // Get schedules for these doctors for the target day\n        // Use profile_id since doctor_availability.doctor_id references profiles.id\n        const profileIds = filteredDoctorsData?.map((d)=>d.profile_id).filter(Boolean) || [];\n        if (profileIds.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: [],\n                message: doctorId ? \"Specified doctor not found\" : serviceId ? \"No doctors available for this service\" : \"No doctors found in organization\"\n            });\n        }\n        console.log(\"DEBUG: Profile IDs for schedule lookup:\", profileIds);\n        console.log(\"DEBUG: Looking for day_of_week:\", dayOfWeek);\n        // Optimize: Get schedules and appointments in parallel\n        const [schedulesResult, appointmentsResult] = await Promise.all([\n            serviceSupabase.from(\"doctor_availability\").select(\"doctor_id, day_of_week, start_time, end_time, is_active\").in(\"doctor_id\", profileIds).eq(\"day_of_week\", dayOfWeek).eq(\"is_active\", true),\n            serviceSupabase.from(\"appointments\").select(\"doctor_id, start_time, end_time\").eq(\"appointment_date\", date).in(\"status\", [\n                \"confirmed\",\n                \"pending\"\n            ])\n        ]);\n        const { data: schedules, error: schedulesError } = schedulesResult;\n        const { data: existingAppointments, error: appointmentsError } = appointmentsResult;\n        if (schedulesError) {\n            console.error(\"Error fetching schedules:\", schedulesError);\n            console.error(\"Query parameters - profileIds:\", profileIds, \"dayOfWeek:\", dayOfWeek);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch doctor schedules\"\n            }, {\n                status: 500\n            });\n        }\n        if (appointmentsError) {\n            console.error(\"Error fetching appointments:\", appointmentsError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to check existing appointments\"\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"DEBUG: Schedules found:\", schedules?.length || 0);\n        console.log(\"DEBUG: Existing appointments:\", existingAppointments?.length || 0);\n        // Combine doctors with their schedules\n        // Match schedules using profile_id since doctor_availability.doctor_id references profiles.id\n        const doctors = filteredDoctorsData?.filter((doctor)=>{\n            const profileId = doctor.profile_id;\n            const doctorSchedules = schedules?.filter((s)=>s.doctor_id === profileId) || [];\n            if (doctorSchedules.length > 0) {\n                doctor.schedules = doctorSchedules;\n                return true;\n            }\n            return false;\n        }) || [];\n        console.log(\"DEBUG: Doctors with schedules:\", doctors.length);\n        // Get service pricing if serviceId is provided\n        let servicePrice = null;\n        if (serviceId) {\n            const { data: serviceData, error: serviceError } = await serviceSupabase.from(\"services\").select(\"price\").eq(\"id\", serviceId).single();\n            if (!serviceError && serviceData) {\n                servicePrice = serviceData.price;\n            }\n        }\n        // Generate available time slots with deduplication\n        const availableSlots = [];\n        const slotMap = new Map(); // Use map to deduplicate by time + doctor\n        for (const doctor of doctors){\n            const profileId = doctor.profile_id;\n            const doctorAppointments = existingAppointments?.filter((apt)=>apt.doctor_id === profileId) || [];\n            // Get doctor's schedule for the day\n            const schedules = doctor.schedules || [];\n            // Merge overlapping schedules to avoid duplicates\n            const mergedSchedules = mergeSchedules(schedules);\n            for (const schedule of mergedSchedules){\n                if (schedule) {\n                    const slots = generateTimeSlots(schedule.start_time, schedule.end_time, duration, doctorAppointments);\n                    for (const slot of slots){\n                        const profile = doctor.profiles;\n                        const slotKey = `${doctor.id}-${slot.start_time}`;\n                        // Only add if not already exists (prevents duplicates)\n                        if (!slotMap.has(slotKey)) {\n                            const timeSlot = {\n                                start_time: slot.start_time,\n                                end_time: slot.end_time,\n                                doctor_id: doctor.id,\n                                doctor_name: profile ? `Dr. ${profile.first_name} ${profile.last_name}` : `Dr. ${doctor.specialization}`,\n                                specialization: doctor.specialization,\n                                consultation_fee: servicePrice || doctor.consultation_fee,\n                                available: slot.available\n                            };\n                            slotMap.set(slotKey, timeSlot);\n                            availableSlots.push(timeSlot);\n                        }\n                    }\n                }\n            }\n        }\n        // Sort slots by time and filter only available ones\n        const sortedAvailableSlots = availableSlots.filter((slot)=>slot.available).sort((a, b)=>a.start_time.localeCompare(b.start_time));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: sortedAvailableSlots,\n            count: sortedAvailableSlots.length,\n            date: date,\n            day_of_week: dayOfWeek,\n            duration_minutes: duration\n        });\n    } catch (error) {\n        console.error(\"Unexpected error in GET /api/doctors/availability:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * Generate time slots for a given time range\n */ function generateTimeSlots(startTime, endTime, durationMinutes, existingAppointments) {\n    const slots = [];\n    // Convert time strings to minutes since midnight\n    const startMinutes = timeToMinutes(startTime);\n    const endMinutes = timeToMinutes(endTime);\n    // Generate slots\n    for(let currentMinutes = startMinutes; currentMinutes + durationMinutes <= endMinutes; currentMinutes += durationMinutes){\n        const slotStart = minutesToTime(currentMinutes);\n        const slotEnd = minutesToTime(currentMinutes + durationMinutes);\n        // Check if slot conflicts with existing appointments\n        const hasConflict = existingAppointments.some((appointment)=>{\n            const aptStart = timeToMinutes(appointment.start_time);\n            const aptEnd = timeToMinutes(appointment.end_time);\n            return currentMinutes >= aptStart && currentMinutes < aptEnd || currentMinutes + durationMinutes > aptStart && currentMinutes + durationMinutes <= aptEnd || currentMinutes <= aptStart && currentMinutes + durationMinutes >= aptEnd;\n        });\n        slots.push({\n            start_time: slotStart,\n            end_time: slotEnd,\n            available: !hasConflict\n        });\n    }\n    return slots;\n}\n/**\n * Convert time string (HH:MM) to minutes since midnight\n */ function timeToMinutes(timeString) {\n    const parts = timeString.split(\":\").map(Number);\n    const hours = parts[0] || 0;\n    const minutes = parts[1] || 0;\n    return hours * 60 + minutes;\n}\n/**\n * Convert minutes since midnight to time string (HH:MM)\n */ function minutesToTime(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours.toString().padStart(2, \"0\")}:${mins.toString().padStart(2, \"0\")}`;\n}\n/**\n * Merge overlapping schedules to prevent duplicate time slots\n */ function mergeSchedules(schedules) {\n    if (schedules.length <= 1) return schedules;\n    // Sort schedules by start time\n    const sorted = schedules.map((s)=>({\n            start_time: s.start_time,\n            end_time: s.end_time,\n            startMinutes: timeToMinutes(s.start_time),\n            endMinutes: timeToMinutes(s.end_time)\n        })).sort((a, b)=>a.startMinutes - b.startMinutes);\n    const merged = [];\n    let current = sorted[0];\n    for(let i = 1; i < sorted.length; i++){\n        const next = sorted[i];\n        // If current schedule overlaps or is adjacent to next, merge them\n        if (current.endMinutes >= next.startMinutes) {\n            current = {\n                start_time: current.start_time,\n                end_time: current.endMinutes >= next.endMinutes ? current.end_time : next.end_time,\n                startMinutes: current.startMinutes,\n                endMinutes: Math.max(current.endMinutes, next.endMinutes)\n            };\n        } else {\n            // No overlap, add current to merged and move to next\n            merged.push({\n                start_time: current.start_time,\n                end_time: current.end_time\n            });\n            current = next;\n        }\n    }\n    // Add the last schedule\n    merged.push({\n        start_time: current.start_time,\n        end_time: current.end_time\n    });\n    return merged;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9kb2N0b3JzL2F2YWlsYWJpbGl0eS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7Ozs7O0NBS0MsR0FFdUQ7QUFDSDtBQUM3QjtBQUV4QiwyQ0FBMkM7QUFDM0MsTUFBTUcsMEJBQTBCRCx5Q0FBUSxDQUFDO0lBQ3ZDRyxnQkFBZ0JILHlDQUFRLEdBQUdLLElBQUk7SUFDL0JDLFdBQVdOLHlDQUFRLEdBQUdLLElBQUksR0FBR0UsUUFBUTtJQUNyQ0MsVUFBVVIseUNBQVEsR0FBR0ssSUFBSSxHQUFHRSxRQUFRO0lBQ3BDRSxNQUFNVCx5Q0FBUSxHQUFHVSxLQUFLLENBQUM7SUFDdkJDLFVBQVVYLHlDQUFRLEdBQUdZLFNBQVMsQ0FBQ0MsUUFBUUMsSUFBSSxDQUFDZCx5Q0FBUSxHQUFHZ0IsR0FBRyxDQUFDLElBQUlDLEdBQUcsQ0FBQyxNQUFNQyxPQUFPLENBQUM7QUFDbkY7QUFZQSxnQ0FBZ0M7QUFDekIsZUFBZUMsSUFBSUMsT0FBb0I7SUFDNUMsSUFBSTtRQUNGLE1BQU1DLFdBQVcsTUFBTXRCLGtFQUFZQTtRQUNuQyxNQUFNLEVBQUV1QixZQUFZLEVBQUUsR0FBRyxJQUFJQyxJQUFJSCxRQUFRSSxHQUFHO1FBRTVDLHdDQUF3QztRQUN4QyxNQUFNQyxjQUFjO1lBQ2xCdEIsZ0JBQWdCbUIsYUFBYUksR0FBRyxDQUFDO1lBQ2pDcEIsV0FBV2dCLGFBQWFJLEdBQUcsQ0FBQyxnQkFBZ0JDO1lBQzVDbkIsVUFBVWMsYUFBYUksR0FBRyxDQUFDLGVBQWVDO1lBQzFDbEIsTUFBTWEsYUFBYUksR0FBRyxDQUFDO1lBQ3ZCZixVQUFVVyxhQUFhSSxHQUFHLENBQUMsZUFBZTtRQUM1QztRQUVBLE1BQU1FLG1CQUFtQjNCLHdCQUF3QjRCLFNBQVMsQ0FBQ0o7UUFDM0QsSUFBSSxDQUFDRyxpQkFBaUJFLE9BQU8sRUFBRTtZQUM3QkMsUUFBUUMsS0FBSyxDQUFDLDZDQUE2QztnQkFDekRQO2dCQUNBUSxRQUFRTCxpQkFBaUJJLEtBQUssQ0FBQ0MsTUFBTTtZQUN2QztZQUNBLE9BQU9uQyxxREFBWUEsQ0FBQ29DLElBQUksQ0FDdEI7Z0JBQ0VGLE9BQU87Z0JBQ1BHLFNBQVNQLGlCQUFpQkksS0FBSyxDQUFDQyxNQUFNO2dCQUN0Q0csVUFBVVg7WUFDWixHQUNBO2dCQUFFWSxRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNLEVBQUVsQyxjQUFjLEVBQUVHLFNBQVMsRUFBRUUsUUFBUSxFQUFFQyxJQUFJLEVBQUVFLFFBQVEsRUFBRSxHQUFHaUIsaUJBQWlCVSxJQUFJO1FBRXJGLGlDQUFpQztRQUNqQyxNQUFNQyxhQUFhLElBQUlDLEtBQUsvQjtRQUM1QixNQUFNZ0MsWUFBWUYsV0FBV0csTUFBTSxJQUFJLDJCQUEyQjtRQUVsRSxpRUFBaUU7UUFDakVYLFFBQVFZLEdBQUcsQ0FBQyxzQ0FBc0N4QyxnQkFBZ0IsUUFBUXNDO1FBRTFFLDZCQUE2QjtRQUM3QixNQUFNLEVBQUVILE1BQU0sRUFBRU0sSUFBSSxFQUFFLEVBQUVaLE9BQU9hLFNBQVMsRUFBRSxHQUFHLE1BQU14QixTQUFTeUIsSUFBSSxDQUFDQyxPQUFPO1FBQ3hFaEIsUUFBUVksR0FBRyxDQUFDLHFCQUFxQkMsTUFBTUksTUFBTSxXQUFXLGVBQWVILFdBQVdJLFdBQVc7UUFFN0YsdURBQXVEO1FBQ3ZELE1BQU0sRUFBRWxELGNBQWNtRCxtQkFBbUIsRUFBRSxHQUFHLE1BQU0sa01BQU87UUFDM0QsTUFBTUMsa0JBQWtCRDtRQUV4QixJQUFJRTtRQUVKLElBQUk5QyxXQUFXO1lBQ2IsNERBQTREO1lBQzVELE1BQU0sRUFBRWdDLE1BQU1lLGNBQWMsRUFBRXJCLE9BQU9zQixZQUFZLEVBQUUsR0FBRyxNQUFNSCxnQkFDekRJLElBQUksQ0FBQyxtQkFDTEMsTUFBTSxDQUFDLGFBQ1BDLEVBQUUsQ0FBQyxjQUFjbkQ7WUFFcEIsSUFBSWdELGNBQWM7Z0JBQ2hCdkIsUUFBUUMsS0FBSyxDQUFDLG1DQUFtQ3NCO2dCQUNqRCxPQUFPeEQscURBQVlBLENBQUNvQyxJQUFJLENBQ3RCO29CQUFFRixPQUFPO2dCQUFrQyxHQUMzQztvQkFBRUssUUFBUTtnQkFBSTtZQUVsQjtZQUVBLE1BQU1xQixZQUFZTCxnQkFBZ0JNLElBQUlDLENBQUFBLEtBQU1BLEdBQUdDLFNBQVMsS0FBSyxFQUFFO1lBRS9ELElBQUlILFVBQVVJLE1BQU0sS0FBSyxHQUFHO2dCQUMxQixPQUFPaEUscURBQVlBLENBQUNvQyxJQUFJLENBQUM7b0JBQ3ZCSixTQUFTO29CQUNUaUMsT0FBTyxFQUFFO29CQUNUZCxTQUFTO2dCQUNYO1lBQ0Y7WUFFQSxzREFBc0Q7WUFDdEQsZ0ZBQWdGO1lBQ2hGLDBDQUEwQztZQUMxQyxNQUFNLEVBQUVYLE1BQU0wQixlQUFlLEVBQUVoQyxPQUFPaUMsWUFBWSxFQUFFLEdBQUcsTUFBTWQsZ0JBQzFESSxJQUFJLENBQUMsV0FDTEMsTUFBTSxDQUFDLENBQUM7Ozs7OztRQU1ULENBQUMsRUFDQUMsRUFBRSxDQUFDLG1CQUFtQnRELGdCQUN0QnNELEVBQUUsQ0FBQyxnQkFBZ0IsTUFDbkJTLEVBQUUsQ0FBQyxjQUFjUjtZQUVwQk4sYUFBYTtnQkFBRWQsTUFBTTBCO2dCQUFpQmhDLE9BQU9pQztZQUFhO1FBQzVELE9BQU87WUFDTCwrQkFBK0I7WUFDL0IsTUFBTSxFQUFFM0IsTUFBTTZCLGNBQWMsRUFBRW5DLE9BQU9pQyxZQUFZLEVBQUUsR0FBRyxNQUFNZCxnQkFDekRJLElBQUksQ0FBQyxXQUNMQyxNQUFNLENBQUMsQ0FBQzs7Ozs7O1FBTVQsQ0FBQyxFQUNBQyxFQUFFLENBQUMsbUJBQW1CdEQsZ0JBQ3RCc0QsRUFBRSxDQUFDLGdCQUFnQjtZQUV0QkwsYUFBYTtnQkFBRWQsTUFBTTZCO2dCQUFnQm5DLE9BQU9pQztZQUFhO1FBQzNEO1FBRUEsTUFBTSxFQUFFM0IsTUFBTThCLFdBQVcsRUFBRXBDLE9BQU9pQyxZQUFZLEVBQUUsR0FBR2I7UUFFbkQsSUFBSWEsY0FBYztZQUNoQmxDLFFBQVFDLEtBQUssQ0FBQywyQkFBMkJpQztZQUN6QyxPQUFPbkUscURBQVlBLENBQUNvQyxJQUFJLENBQ3RCO2dCQUFFRixPQUFPO1lBQXNDLEdBQy9DO2dCQUFFSyxRQUFRO1lBQUk7UUFFbEI7UUFFQU4sUUFBUVksR0FBRyxDQUFDLCtCQUErQnlCLGFBQWFOLFVBQVU7UUFFbEUsb0RBQW9EO1FBQ3BELElBQUlPLHNCQUFzQkQ7UUFDMUIsSUFBSTVELFlBQVk0RCxhQUFhO1lBQzNCQyxzQkFBc0JELFlBQVlFLE1BQU0sQ0FBQ0MsQ0FBQUEsU0FBVUEsT0FBT3ZCLEVBQUUsS0FBS3hDO1lBQ2pFdUIsUUFBUVksR0FBRyxDQUFDLHVDQUF1Q25DLFVBQVUsVUFBVTZELG9CQUFvQlAsTUFBTTtZQUVqRyxJQUFJTyxvQkFBb0JQLE1BQU0sS0FBSyxHQUFHO2dCQUNwQyxPQUFPaEUscURBQVlBLENBQUNvQyxJQUFJLENBQUM7b0JBQ3ZCSixTQUFTO29CQUNUUSxNQUFNLEVBQUU7b0JBQ1JXLFNBQVM7Z0JBQ1g7WUFDRjtRQUNGO1FBRUEscURBQXFEO1FBQ3JELDRFQUE0RTtRQUM1RSxNQUFNdUIsYUFBYUgscUJBQXFCVixJQUFJYyxDQUFBQSxJQUFLQSxFQUFFQyxVQUFVLEVBQUVKLE9BQU9LLFlBQVksRUFBRTtRQUNwRixJQUFJSCxXQUFXVixNQUFNLEtBQUssR0FBRztZQUMzQixPQUFPaEUscURBQVlBLENBQUNvQyxJQUFJLENBQUM7Z0JBQ3ZCSixTQUFTO2dCQUNUUSxNQUFNLEVBQUU7Z0JBQ1JXLFNBQVN6QyxXQUFXLCtCQUFnQ0YsWUFBWSwwQ0FBMEM7WUFDNUc7UUFDRjtRQUVBeUIsUUFBUVksR0FBRyxDQUFDLDJDQUEyQzZCO1FBQ3ZEekMsUUFBUVksR0FBRyxDQUFDLG1DQUFtQ0Y7UUFFL0MsdURBQXVEO1FBQ3ZELE1BQU0sQ0FBQ21DLGlCQUFpQkMsbUJBQW1CLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO1lBQzlENUIsZ0JBQ0dJLElBQUksQ0FBQyx1QkFDTEMsTUFBTSxDQUFDLDJEQUNQVSxFQUFFLENBQUMsYUFBYU0sWUFDaEJmLEVBQUUsQ0FBQyxlQUFlaEIsV0FDbEJnQixFQUFFLENBQUMsYUFBYTtZQUNuQk4sZ0JBQ0dJLElBQUksQ0FBQyxnQkFDTEMsTUFBTSxDQUFDLG1DQUNQQyxFQUFFLENBQUMsb0JBQW9CaEQsTUFDdkJ5RCxFQUFFLENBQUMsVUFBVTtnQkFBQztnQkFBYTthQUFVO1NBQ3pDO1FBRUQsTUFBTSxFQUFFNUIsTUFBTTBDLFNBQVMsRUFBRWhELE9BQU9pRCxjQUFjLEVBQUUsR0FBR0w7UUFDbkQsTUFBTSxFQUFFdEMsTUFBTTRDLG9CQUFvQixFQUFFbEQsT0FBT21ELGlCQUFpQixFQUFFLEdBQUdOO1FBRWpFLElBQUlJLGdCQUFnQjtZQUNsQmxELFFBQVFDLEtBQUssQ0FBQyw2QkFBNkJpRDtZQUMzQ2xELFFBQVFDLEtBQUssQ0FBQyxrQ0FBa0N3QyxZQUFZLGNBQWMvQjtZQUMxRSxPQUFPM0MscURBQVlBLENBQUNvQyxJQUFJLENBQ3RCO2dCQUFFRixPQUFPO1lBQW1DLEdBQzVDO2dCQUFFSyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxJQUFJOEMsbUJBQW1CO1lBQ3JCcEQsUUFBUUMsS0FBSyxDQUFDLGdDQUFnQ21EO1lBQzlDLE9BQU9yRixxREFBWUEsQ0FBQ29DLElBQUksQ0FDdEI7Z0JBQUVGLE9BQU87WUFBd0MsR0FDakQ7Z0JBQUVLLFFBQVE7WUFBSTtRQUVsQjtRQUVBTixRQUFRWSxHQUFHLENBQUMsMkJBQTJCcUMsV0FBV2xCLFVBQVU7UUFDNUQvQixRQUFRWSxHQUFHLENBQUMsaUNBQWlDdUMsc0JBQXNCcEIsVUFBVTtRQUU3RSx1Q0FBdUM7UUFDdkMsOEZBQThGO1FBQzlGLE1BQU1zQixVQUFVZixxQkFBcUJDLE9BQU9DLENBQUFBO1lBQzFDLE1BQU1jLFlBQVlkLE9BQU9HLFVBQVU7WUFDbkMsTUFBTVksa0JBQWtCTixXQUFXVixPQUFPaUIsQ0FBQUEsSUFBS0EsRUFBRTFCLFNBQVMsS0FBS3dCLGNBQWMsRUFBRTtZQUMvRSxJQUFJQyxnQkFBZ0J4QixNQUFNLEdBQUcsR0FBRztnQkFDOUJTLE9BQU9TLFNBQVMsR0FBR007Z0JBQ25CLE9BQU87WUFDVDtZQUNBLE9BQU87UUFDVCxNQUFNLEVBQUU7UUFFUnZELFFBQVFZLEdBQUcsQ0FBQyxrQ0FBa0N5QyxRQUFRdEIsTUFBTTtRQUU1RCwrQ0FBK0M7UUFDL0MsSUFBSTBCLGVBQWU7UUFDbkIsSUFBSWxGLFdBQVc7WUFDYixNQUFNLEVBQUVnQyxNQUFNbUQsV0FBVyxFQUFFekQsT0FBT3NCLFlBQVksRUFBRSxHQUFHLE1BQU1ILGdCQUN0REksSUFBSSxDQUFDLFlBQ0xDLE1BQU0sQ0FBQyxTQUNQQyxFQUFFLENBQUMsTUFBTW5ELFdBQ1RvRixNQUFNO1lBRVQsSUFBSSxDQUFDcEMsZ0JBQWdCbUMsYUFBYTtnQkFDaENELGVBQWVDLFlBQVlFLEtBQUs7WUFDbEM7UUFDRjtRQUVBLG1EQUFtRDtRQUNuRCxNQUFNQyxpQkFBNkIsRUFBRTtRQUNyQyxNQUFNQyxVQUFVLElBQUlDLE9BQXlCLDBDQUEwQztRQUV2RixLQUFLLE1BQU12QixVQUFVYSxRQUFTO1lBQzVCLE1BQU1DLFlBQVlkLE9BQU9HLFVBQVU7WUFDbkMsTUFBTXFCLHFCQUFxQmIsc0JBQXNCWixPQUMvQzBCLENBQUFBLE1BQU9BLElBQUluQyxTQUFTLEtBQUt3QixjQUN0QixFQUFFO1lBRVAsb0NBQW9DO1lBQ3BDLE1BQU1MLFlBQVlULE9BQU9TLFNBQVMsSUFBSSxFQUFFO1lBRXhDLGtEQUFrRDtZQUNsRCxNQUFNaUIsa0JBQWtCQyxlQUFlbEI7WUFFdkMsS0FBSyxNQUFNbUIsWUFBWUYsZ0JBQWlCO2dCQUN0QyxJQUFJRSxVQUFVO29CQUNaLE1BQU1wQyxRQUFRcUMsa0JBQ1pELFNBQVNFLFVBQVUsRUFDbkJGLFNBQVNHLFFBQVEsRUFDakIzRixVQUNBb0Y7b0JBR0YsS0FBSyxNQUFNUSxRQUFReEMsTUFBTzt3QkFDeEIsTUFBTXlDLFVBQVVqQyxPQUFPa0MsUUFBUTt3QkFDL0IsTUFBTUMsVUFBVSxDQUFDLEVBQUVuQyxPQUFPdkIsRUFBRSxDQUFDLENBQUMsRUFBRXVELEtBQUtGLFVBQVUsQ0FBQyxDQUFDO3dCQUVqRCx1REFBdUQ7d0JBQ3ZELElBQUksQ0FBQ1IsUUFBUWMsR0FBRyxDQUFDRCxVQUFVOzRCQUN6QixNQUFNRSxXQUFxQjtnQ0FDekJQLFlBQVlFLEtBQUtGLFVBQVU7Z0NBQzNCQyxVQUFVQyxLQUFLRCxRQUFRO2dDQUN2QnpDLFdBQVdVLE9BQU92QixFQUFFO2dDQUNwQjZELGFBQWFMLFVBQ1QsQ0FBQyxJQUFJLEVBQUVBLFFBQVFNLFVBQVUsQ0FBQyxDQUFDLEVBQUVOLFFBQVFPLFNBQVMsQ0FBQyxDQUFDLEdBQ2hELENBQUMsSUFBSSxFQUFFeEMsT0FBT3lDLGNBQWMsQ0FBQyxDQUFDO2dDQUNsQ0EsZ0JBQWdCekMsT0FBT3lDLGNBQWM7Z0NBQ3JDQyxrQkFBa0J6QixnQkFBZ0JqQixPQUFPMEMsZ0JBQWdCO2dDQUN6REMsV0FBV1gsS0FBS1csU0FBUzs0QkFDM0I7NEJBRUFyQixRQUFRc0IsR0FBRyxDQUFDVCxTQUFTRTs0QkFDckJoQixlQUFld0IsSUFBSSxDQUFDUjt3QkFDdEI7b0JBQ0Y7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsb0RBQW9EO1FBQ3BELE1BQU1TLHVCQUF1QnpCLGVBQzFCdEIsTUFBTSxDQUFDaUMsQ0FBQUEsT0FBUUEsS0FBS1csU0FBUyxFQUM3QkksSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1ELEVBQUVsQixVQUFVLENBQUNvQixhQUFhLENBQUNELEVBQUVuQixVQUFVO1FBRXpELE9BQU92RyxxREFBWUEsQ0FBQ29DLElBQUksQ0FBQztZQUN2QkosU0FBUztZQUNUUSxNQUFNK0U7WUFDTkssT0FBT0wscUJBQXFCdkQsTUFBTTtZQUNsQ3JELE1BQU1BO1lBQ05rSCxhQUFhbEY7WUFDYm1GLGtCQUFrQmpIO1FBQ3BCO0lBRUYsRUFBRSxPQUFPcUIsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsc0RBQXNEQTtRQUNwRSxPQUFPbEMscURBQVlBLENBQUNvQyxJQUFJLENBQ3RCO1lBQUVGLE9BQU87UUFBd0IsR0FDakM7WUFBRUssUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNELFNBQVMrRCxrQkFDUHlCLFNBQWlCLEVBQ2pCQyxPQUFlLEVBQ2ZDLGVBQXVCLEVBQ3ZCN0Msb0JBQXFFO0lBRXJFLE1BQU1uQixRQUE2RSxFQUFFO0lBRXJGLGlEQUFpRDtJQUNqRCxNQUFNaUUsZUFBZUMsY0FBY0o7SUFDbkMsTUFBTUssYUFBYUQsY0FBY0g7SUFFakMsaUJBQWlCO0lBQ2pCLElBQUssSUFBSUssaUJBQWlCSCxjQUFjRyxpQkFBaUJKLG1CQUFtQkcsWUFBWUMsa0JBQWtCSixnQkFBaUI7UUFDekgsTUFBTUssWUFBWUMsY0FBY0Y7UUFDaEMsTUFBTUcsVUFBVUQsY0FBY0YsaUJBQWlCSjtRQUUvQyxxREFBcUQ7UUFDckQsTUFBTVEsY0FBY3JELHFCQUFxQnNELElBQUksQ0FBQ0MsQ0FBQUE7WUFDNUMsTUFBTUMsV0FBV1QsY0FBY1EsWUFBWXBDLFVBQVU7WUFDckQsTUFBTXNDLFNBQVNWLGNBQWNRLFlBQVluQyxRQUFRO1lBRWpELE9BQ0Usa0JBQW1Cb0MsWUFBWVAsaUJBQWlCUSxVQUMvQ1IsaUJBQWlCSixrQkFBa0JXLFlBQVlQLGlCQUFpQkosbUJBQW1CWSxVQUNuRlIsa0JBQWtCTyxZQUFZUCxpQkFBaUJKLG1CQUFtQlk7UUFFdkU7UUFFQTVFLE1BQU1xRCxJQUFJLENBQUM7WUFDVGYsWUFBWStCO1lBQ1o5QixVQUFVZ0M7WUFDVnBCLFdBQVcsQ0FBQ3FCO1FBQ2Q7SUFDRjtJQUVBLE9BQU94RTtBQUNUO0FBRUE7O0NBRUMsR0FDRCxTQUFTa0UsY0FBY1csVUFBa0I7SUFDdkMsTUFBTUMsUUFBUUQsV0FBV0UsS0FBSyxDQUFDLEtBQUtuRixHQUFHLENBQUM5QztJQUN4QyxNQUFNa0ksUUFBUUYsS0FBSyxDQUFDLEVBQUUsSUFBSTtJQUMxQixNQUFNRyxVQUFVSCxLQUFLLENBQUMsRUFBRSxJQUFJO0lBQzVCLE9BQU9FLFFBQVEsS0FBS0M7QUFDdEI7QUFFQTs7Q0FFQyxHQUNELFNBQVNYLGNBQWNXLE9BQWU7SUFDcEMsTUFBTUQsUUFBUUUsS0FBS0MsS0FBSyxDQUFDRixVQUFVO0lBQ25DLE1BQU1HLE9BQU9ILFVBQVU7SUFDdkIsT0FBTyxDQUFDLEVBQUVELE1BQU1LLFFBQVEsR0FBR0MsUUFBUSxDQUFDLEdBQUcsS0FBSyxDQUFDLEVBQUVGLEtBQUtDLFFBQVEsR0FBR0MsUUFBUSxDQUFDLEdBQUcsS0FBSyxDQUFDO0FBQ25GO0FBRUE7O0NBRUMsR0FDRCxTQUFTbkQsZUFBZWxCLFNBQTBEO0lBQ2hGLElBQUlBLFVBQVVsQixNQUFNLElBQUksR0FBRyxPQUFPa0I7SUFFbEMsK0JBQStCO0lBQy9CLE1BQU1zRSxTQUFTdEUsVUFDWnJCLEdBQUcsQ0FBQzRCLENBQUFBLElBQU07WUFDVGMsWUFBWWQsRUFBRWMsVUFBVTtZQUN4QkMsVUFBVWYsRUFBRWUsUUFBUTtZQUNwQjBCLGNBQWNDLGNBQWMxQyxFQUFFYyxVQUFVO1lBQ3hDNkIsWUFBWUQsY0FBYzFDLEVBQUVlLFFBQVE7UUFDdEMsSUFDQ2dCLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxFQUFFUyxZQUFZLEdBQUdSLEVBQUVRLFlBQVk7SUFFakQsTUFBTXVCLFNBQTBELEVBQUU7SUFDbEUsSUFBSUMsVUFBVUYsTUFBTSxDQUFDLEVBQUU7SUFFdkIsSUFBSyxJQUFJRyxJQUFJLEdBQUdBLElBQUlILE9BQU94RixNQUFNLEVBQUUyRixJQUFLO1FBQ3RDLE1BQU1DLE9BQU9KLE1BQU0sQ0FBQ0csRUFBRTtRQUV0QixrRUFBa0U7UUFDbEUsSUFBSUQsUUFBUXRCLFVBQVUsSUFBSXdCLEtBQUsxQixZQUFZLEVBQUU7WUFDM0N3QixVQUFVO2dCQUNSbkQsWUFBWW1ELFFBQVFuRCxVQUFVO2dCQUM5QkMsVUFBVWtELFFBQVF0QixVQUFVLElBQUl3QixLQUFLeEIsVUFBVSxHQUFHc0IsUUFBUWxELFFBQVEsR0FBR29ELEtBQUtwRCxRQUFRO2dCQUNsRjBCLGNBQWN3QixRQUFReEIsWUFBWTtnQkFDbENFLFlBQVllLEtBQUtoSSxHQUFHLENBQUN1SSxRQUFRdEIsVUFBVSxFQUFFd0IsS0FBS3hCLFVBQVU7WUFDMUQ7UUFDRixPQUFPO1lBQ0wscURBQXFEO1lBQ3JEcUIsT0FBT25DLElBQUksQ0FBQztnQkFDVmYsWUFBWW1ELFFBQVFuRCxVQUFVO2dCQUM5QkMsVUFBVWtELFFBQVFsRCxRQUFRO1lBQzVCO1lBQ0FrRCxVQUFVRTtRQUNaO0lBQ0Y7SUFFQSx3QkFBd0I7SUFDeEJILE9BQU9uQyxJQUFJLENBQUM7UUFDVmYsWUFBWW1ELFFBQVFuRCxVQUFVO1FBQzlCQyxVQUFVa0QsUUFBUWxELFFBQVE7SUFDNUI7SUFFQSxPQUFPaUQ7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2FnZW5kYWxvLy4vc3JjL2FwcC9hcGkvZG9jdG9ycy9hdmFpbGFiaWxpdHkvcm91dGUudHM/ODRhYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEFQSSBSb3V0ZSBmb3IgRG9jdG9yIEF2YWlsYWJpbGl0eVxuICogUHJvdmlkZXMgYXZhaWxhYmxlIHRpbWUgc2xvdHMgZm9yIGFwcG9pbnRtZW50IGJvb2tpbmdcbiAqXG4gKiBAcm91dGUgR0VUIC9hcGkvZG9jdG9ycy9hdmFpbGFiaWxpdHkgLSBHZXQgYXZhaWxhYmxlIHRpbWUgc2xvdHNcbiAqL1xuXG5pbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQC9saWIvc3VwYWJhc2Uvc2VydmVyJztcbmltcG9ydCB7IHogfSBmcm9tICd6b2QnO1xuXG4vLyBWYWxpZGF0aW9uIHNjaGVtYSBmb3IgYXZhaWxhYmlsaXR5IHF1ZXJ5XG5jb25zdCBhdmFpbGFiaWxpdHlRdWVyeVNjaGVtYSA9IHoub2JqZWN0KHtcbiAgb3JnYW5pemF0aW9uSWQ6IHouc3RyaW5nKCkudXVpZCgpLFxuICBzZXJ2aWNlSWQ6IHouc3RyaW5nKCkudXVpZCgpLm9wdGlvbmFsKCksXG4gIGRvY3RvcklkOiB6LnN0cmluZygpLnV1aWQoKS5vcHRpb25hbCgpLFxuICBkYXRlOiB6LnN0cmluZygpLnJlZ2V4KC9eXFxkezR9LVxcZHsyfS1cXGR7Mn0kLyksIC8vIFlZWVktTU0tREQgZm9ybWF0XG4gIGR1cmF0aW9uOiB6LnN0cmluZygpLnRyYW5zZm9ybShOdW1iZXIpLnBpcGUoei5udW1iZXIoKS5taW4oMTUpLm1heCgyNDApKS5kZWZhdWx0KCczMCcpLCAvLyBtaW51dGVzXG59KTtcblxuaW50ZXJmYWNlIFRpbWVTbG90IHtcbiAgc3RhcnRfdGltZTogc3RyaW5nO1xuICBlbmRfdGltZTogc3RyaW5nO1xuICBkb2N0b3JfaWQ6IHN0cmluZztcbiAgZG9jdG9yX25hbWU6IHN0cmluZztcbiAgc3BlY2lhbGl6YXRpb246IHN0cmluZztcbiAgY29uc3VsdGF0aW9uX2ZlZTogbnVtYmVyO1xuICBhdmFpbGFibGU6IGJvb2xlYW47XG59XG5cbi8vIEdFVCAvYXBpL2RvY3RvcnMvYXZhaWxhYmlsaXR5XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3Qgc3VwYWJhc2UgPSBhd2FpdCBjcmVhdGVDbGllbnQoKTtcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG5cbiAgICAvLyBFeHRyYWN0IGFuZCB2YWxpZGF0ZSBxdWVyeSBwYXJhbWV0ZXJzXG4gICAgY29uc3QgcXVlcnlQYXJhbXMgPSB7XG4gICAgICBvcmdhbml6YXRpb25JZDogc2VhcmNoUGFyYW1zLmdldCgnb3JnYW5pemF0aW9uSWQnKSxcbiAgICAgIHNlcnZpY2VJZDogc2VhcmNoUGFyYW1zLmdldCgnc2VydmljZUlkJykgfHwgdW5kZWZpbmVkLFxuICAgICAgZG9jdG9ySWQ6IHNlYXJjaFBhcmFtcy5nZXQoJ2RvY3RvcklkJykgfHwgdW5kZWZpbmVkLFxuICAgICAgZGF0ZTogc2VhcmNoUGFyYW1zLmdldCgnZGF0ZScpLFxuICAgICAgZHVyYXRpb246IHNlYXJjaFBhcmFtcy5nZXQoJ2R1cmF0aW9uJykgfHwgJzMwJyxcbiAgICB9O1xuXG4gICAgY29uc3QgdmFsaWRhdGlvblJlc3VsdCA9IGF2YWlsYWJpbGl0eVF1ZXJ5U2NoZW1hLnNhZmVQYXJzZShxdWVyeVBhcmFtcyk7XG4gICAgaWYgKCF2YWxpZGF0aW9uUmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1ZhbGlkYXRpb24gZmFpbGVkIGZvciBhdmFpbGFiaWxpdHkgcXVlcnk6Jywge1xuICAgICAgICBxdWVyeVBhcmFtcyxcbiAgICAgICAgZXJyb3JzOiB2YWxpZGF0aW9uUmVzdWx0LmVycm9yLmVycm9yc1xuICAgICAgfSk7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHtcbiAgICAgICAgICBlcnJvcjogJ0ludmFsaWQgcXVlcnkgcGFyYW1ldGVycycsXG4gICAgICAgICAgZGV0YWlsczogdmFsaWRhdGlvblJlc3VsdC5lcnJvci5lcnJvcnMsXG4gICAgICAgICAgcmVjZWl2ZWQ6IHF1ZXJ5UGFyYW1zXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCB7IG9yZ2FuaXphdGlvbklkLCBzZXJ2aWNlSWQsIGRvY3RvcklkLCBkYXRlLCBkdXJhdGlvbiB9ID0gdmFsaWRhdGlvblJlc3VsdC5kYXRhO1xuXG4gICAgLy8gUGFyc2UgZGF0ZSBhbmQgZ2V0IGRheSBvZiB3ZWVrXG4gICAgY29uc3QgdGFyZ2V0RGF0ZSA9IG5ldyBEYXRlKGRhdGUpO1xuICAgIGNvbnN0IGRheU9mV2VlayA9IHRhcmdldERhdGUuZ2V0RGF5KCk7IC8vIDAgPSBTdW5kYXksIDYgPSBTYXR1cmRheVxuXG4gICAgLy8gVXNlIGEgc2ltcGxpZmllZCBhcHByb2FjaCAtIGdldCBhbGwgZG9jdG9ycyBmaXJzdCwgdGhlbiBmaWx0ZXJcbiAgICBjb25zb2xlLmxvZygnRmV0Y2hpbmcgZG9jdG9ycyBmb3Igb3JnYW5pemF0aW9uOicsIG9yZ2FuaXphdGlvbklkLCAnZGF5OicsIGRheU9mV2Vlayk7XG5cbiAgICAvLyBDaGVjayBhdXRoZW50aWNhdGlvbiBmaXJzdFxuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0sIGVycm9yOiBhdXRoRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuICAgIGNvbnNvbGUubG9nKCdERUJVRzogQXV0aCB1c2VyOicsIHVzZXI/LmlkIHx8ICdObyB1c2VyJywgJ0F1dGggZXJyb3I6JywgYXV0aEVycm9yPy5tZXNzYWdlIHx8ICdOb25lJyk7XG5cbiAgICAvLyBVc2Ugc2VydmljZSByb2xlIGNsaWVudCBmb3IgcHVibGljIGF2YWlsYWJpbGl0eSBkYXRhXG4gICAgY29uc3QgeyBjcmVhdGVDbGllbnQ6IGNyZWF0ZVNlcnZpY2VDbGllbnQgfSA9IGF3YWl0IGltcG9ydCgnQC9saWIvc3VwYWJhc2Uvc2VydmljZScpO1xuICAgIGNvbnN0IHNlcnZpY2VTdXBhYmFzZSA9IGNyZWF0ZVNlcnZpY2VDbGllbnQoKTtcblxuICAgIGxldCBhbGxEb2N0b3JzO1xuXG4gICAgaWYgKHNlcnZpY2VJZCkge1xuICAgICAgLy8gRmlyc3QgZ2V0IGRvY3RvciBJRHMgd2hvIGNhbiBwcm92aWRlIHRoZSBzcGVjaWZpYyBzZXJ2aWNlXG4gICAgICBjb25zdCB7IGRhdGE6IGRvY3RvclNlcnZpY2VzLCBlcnJvcjogc2VydmljZUVycm9yIH0gPSBhd2FpdCBzZXJ2aWNlU3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ2RvY3Rvcl9zZXJ2aWNlcycpXG4gICAgICAgIC5zZWxlY3QoJ2RvY3Rvcl9pZCcpXG4gICAgICAgIC5lcSgnc2VydmljZV9pZCcsIHNlcnZpY2VJZCk7XG5cbiAgICAgIGlmIChzZXJ2aWNlRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgZG9jdG9yIHNlcnZpY2VzOicsIHNlcnZpY2VFcnJvcik7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIGZldGNoIGRvY3RvciBzZXJ2aWNlcycgfSxcbiAgICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgICAgKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgZG9jdG9ySWRzID0gZG9jdG9yU2VydmljZXM/Lm1hcChkcyA9PiBkcy5kb2N0b3JfaWQpIHx8IFtdO1xuXG4gICAgICBpZiAoZG9jdG9ySWRzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgc2xvdHM6IFtdLFxuICAgICAgICAgIG1lc3NhZ2U6ICdObyBkb2N0b3JzIGF2YWlsYWJsZSBmb3IgdGhpcyBzZXJ2aWNlJ1xuICAgICAgICB9KTtcbiAgICAgIH1cblxuICAgICAgLy8gRmlsdGVyIGRvY3RvcnMgd2hvIGNhbiBwcm92aWRlIHRoZSBzcGVjaWZpYyBzZXJ2aWNlXG4gICAgICAvLyBDUklUSUNBTCBGSVg6IGRvY3RvcklkcyBjb250YWlucyBwcm9maWxlX2lkIHZhbHVlcyBmcm9tIGRvY3Rvcl9zZXJ2aWNlcyB0YWJsZVxuICAgICAgLy8gV2UgbmVlZCB0byBzZWFyY2ggYnkgcHJvZmlsZV9pZCwgbm90IGlkXG4gICAgICBjb25zdCB7IGRhdGE6IGZpbHRlcmVkRG9jdG9ycywgZXJyb3I6IGRvY3RvcnNFcnJvciB9ID0gYXdhaXQgc2VydmljZVN1cGFiYXNlXG4gICAgICAgIC5mcm9tKCdkb2N0b3JzJylcbiAgICAgICAgLnNlbGVjdChgXG4gICAgICAgICAgaWQsXG4gICAgICAgICAgcHJvZmlsZV9pZCxcbiAgICAgICAgICBzcGVjaWFsaXphdGlvbixcbiAgICAgICAgICBjb25zdWx0YXRpb25fZmVlLFxuICAgICAgICAgIHByb2ZpbGVzKGlkLCBmaXJzdF9uYW1lLCBsYXN0X25hbWUsIGVtYWlsKVxuICAgICAgICBgKVxuICAgICAgICAuZXEoJ29yZ2FuaXphdGlvbl9pZCcsIG9yZ2FuaXphdGlvbklkKVxuICAgICAgICAuZXEoJ2lzX2F2YWlsYWJsZScsIHRydWUpXG4gICAgICAgIC5pbigncHJvZmlsZV9pZCcsIGRvY3Rvcklkcyk7XG5cbiAgICAgIGFsbERvY3RvcnMgPSB7IGRhdGE6IGZpbHRlcmVkRG9jdG9ycywgZXJyb3I6IGRvY3RvcnNFcnJvciB9O1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBSZXR1cm4gYWxsIGF2YWlsYWJsZSBkb2N0b3JzXG4gICAgICBjb25zdCB7IGRhdGE6IGFsbERvY3RvcnNEYXRhLCBlcnJvcjogZG9jdG9yc0Vycm9yIH0gPSBhd2FpdCBzZXJ2aWNlU3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ2RvY3RvcnMnKVxuICAgICAgICAuc2VsZWN0KGBcbiAgICAgICAgICBpZCxcbiAgICAgICAgICBwcm9maWxlX2lkLFxuICAgICAgICAgIHNwZWNpYWxpemF0aW9uLFxuICAgICAgICAgIGNvbnN1bHRhdGlvbl9mZWUsXG4gICAgICAgICAgcHJvZmlsZXMoaWQsIGZpcnN0X25hbWUsIGxhc3RfbmFtZSwgZW1haWwpXG4gICAgICAgIGApXG4gICAgICAgIC5lcSgnb3JnYW5pemF0aW9uX2lkJywgb3JnYW5pemF0aW9uSWQpXG4gICAgICAgIC5lcSgnaXNfYXZhaWxhYmxlJywgdHJ1ZSk7XG5cbiAgICAgIGFsbERvY3RvcnMgPSB7IGRhdGE6IGFsbERvY3RvcnNEYXRhLCBlcnJvcjogZG9jdG9yc0Vycm9yIH07XG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhOiBkb2N0b3JzRGF0YSwgZXJyb3I6IGRvY3RvcnNFcnJvciB9ID0gYWxsRG9jdG9ycztcblxuICAgIGlmIChkb2N0b3JzRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGRvY3RvcnM6JywgZG9jdG9yc0Vycm9yKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ0ZhaWxlZCB0byBmZXRjaCBkb2N0b3IgYXZhaWxhYmlsaXR5JyB9LFxuICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ0RFQlVHOiBBbGwgZG9jdG9ycyBmZXRjaGVkOicsIGRvY3RvcnNEYXRhPy5sZW5ndGggfHwgMCk7XG5cbiAgICAvLyBGaWx0ZXIgYnkgc3BlY2lmaWMgZG9jdG9yIGlmIGRvY3RvcklkIGlzIHByb3ZpZGVkXG4gICAgbGV0IGZpbHRlcmVkRG9jdG9yc0RhdGEgPSBkb2N0b3JzRGF0YTtcbiAgICBpZiAoZG9jdG9ySWQgJiYgZG9jdG9yc0RhdGEpIHtcbiAgICAgIGZpbHRlcmVkRG9jdG9yc0RhdGEgPSBkb2N0b3JzRGF0YS5maWx0ZXIoZG9jdG9yID0+IGRvY3Rvci5pZCA9PT0gZG9jdG9ySWQpO1xuICAgICAgY29uc29sZS5sb2coJ0RFQlVHOiBGaWx0ZXJlZCB0byBzcGVjaWZpYyBkb2N0b3I6JywgZG9jdG9ySWQsICdGb3VuZDonLCBmaWx0ZXJlZERvY3RvcnNEYXRhLmxlbmd0aCk7XG5cbiAgICAgIGlmIChmaWx0ZXJlZERvY3RvcnNEYXRhLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgZGF0YTogW10sXG4gICAgICAgICAgbWVzc2FnZTogJ1NwZWNpZmllZCBkb2N0b3Igbm90IGZvdW5kIG9yIG5vdCBhdmFpbGFibGUnXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEdldCBzY2hlZHVsZXMgZm9yIHRoZXNlIGRvY3RvcnMgZm9yIHRoZSB0YXJnZXQgZGF5XG4gICAgLy8gVXNlIHByb2ZpbGVfaWQgc2luY2UgZG9jdG9yX2F2YWlsYWJpbGl0eS5kb2N0b3JfaWQgcmVmZXJlbmNlcyBwcm9maWxlcy5pZFxuICAgIGNvbnN0IHByb2ZpbGVJZHMgPSBmaWx0ZXJlZERvY3RvcnNEYXRhPy5tYXAoZCA9PiBkLnByb2ZpbGVfaWQpLmZpbHRlcihCb29sZWFuKSB8fCBbXTtcbiAgICBpZiAocHJvZmlsZUlkcy5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgIGRhdGE6IFtdLFxuICAgICAgICBtZXNzYWdlOiBkb2N0b3JJZCA/ICdTcGVjaWZpZWQgZG9jdG9yIG5vdCBmb3VuZCcgOiAoc2VydmljZUlkID8gJ05vIGRvY3RvcnMgYXZhaWxhYmxlIGZvciB0aGlzIHNlcnZpY2UnIDogJ05vIGRvY3RvcnMgZm91bmQgaW4gb3JnYW5pemF0aW9uJylcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCdERUJVRzogUHJvZmlsZSBJRHMgZm9yIHNjaGVkdWxlIGxvb2t1cDonLCBwcm9maWxlSWRzKTtcbiAgICBjb25zb2xlLmxvZygnREVCVUc6IExvb2tpbmcgZm9yIGRheV9vZl93ZWVrOicsIGRheU9mV2Vlayk7XG5cbiAgICAvLyBPcHRpbWl6ZTogR2V0IHNjaGVkdWxlcyBhbmQgYXBwb2ludG1lbnRzIGluIHBhcmFsbGVsXG4gICAgY29uc3QgW3NjaGVkdWxlc1Jlc3VsdCwgYXBwb2ludG1lbnRzUmVzdWx0XSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgIHNlcnZpY2VTdXBhYmFzZVxuICAgICAgICAuZnJvbSgnZG9jdG9yX2F2YWlsYWJpbGl0eScpXG4gICAgICAgIC5zZWxlY3QoJ2RvY3Rvcl9pZCwgZGF5X29mX3dlZWssIHN0YXJ0X3RpbWUsIGVuZF90aW1lLCBpc19hY3RpdmUnKVxuICAgICAgICAuaW4oJ2RvY3Rvcl9pZCcsIHByb2ZpbGVJZHMpXG4gICAgICAgIC5lcSgnZGF5X29mX3dlZWsnLCBkYXlPZldlZWspXG4gICAgICAgIC5lcSgnaXNfYWN0aXZlJywgdHJ1ZSksXG4gICAgICBzZXJ2aWNlU3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ2FwcG9pbnRtZW50cycpXG4gICAgICAgIC5zZWxlY3QoJ2RvY3Rvcl9pZCwgc3RhcnRfdGltZSwgZW5kX3RpbWUnKVxuICAgICAgICAuZXEoJ2FwcG9pbnRtZW50X2RhdGUnLCBkYXRlKVxuICAgICAgICAuaW4oJ3N0YXR1cycsIFsnY29uZmlybWVkJywgJ3BlbmRpbmcnXSlcbiAgICBdKTtcblxuICAgIGNvbnN0IHsgZGF0YTogc2NoZWR1bGVzLCBlcnJvcjogc2NoZWR1bGVzRXJyb3IgfSA9IHNjaGVkdWxlc1Jlc3VsdDtcbiAgICBjb25zdCB7IGRhdGE6IGV4aXN0aW5nQXBwb2ludG1lbnRzLCBlcnJvcjogYXBwb2ludG1lbnRzRXJyb3IgfSA9IGFwcG9pbnRtZW50c1Jlc3VsdDtcblxuICAgIGlmIChzY2hlZHVsZXNFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgc2NoZWR1bGVzOicsIHNjaGVkdWxlc0Vycm9yKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1F1ZXJ5IHBhcmFtZXRlcnMgLSBwcm9maWxlSWRzOicsIHByb2ZpbGVJZHMsICdkYXlPZldlZWs6JywgZGF5T2ZXZWVrKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ0ZhaWxlZCB0byBmZXRjaCBkb2N0b3Igc2NoZWR1bGVzJyB9LFxuICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgaWYgKGFwcG9pbnRtZW50c0Vycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBhcHBvaW50bWVudHM6JywgYXBwb2ludG1lbnRzRXJyb3IpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIGNoZWNrIGV4aXN0aW5nIGFwcG9pbnRtZW50cycgfSxcbiAgICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCdERUJVRzogU2NoZWR1bGVzIGZvdW5kOicsIHNjaGVkdWxlcz8ubGVuZ3RoIHx8IDApO1xuICAgIGNvbnNvbGUubG9nKCdERUJVRzogRXhpc3RpbmcgYXBwb2ludG1lbnRzOicsIGV4aXN0aW5nQXBwb2ludG1lbnRzPy5sZW5ndGggfHwgMCk7XG5cbiAgICAvLyBDb21iaW5lIGRvY3RvcnMgd2l0aCB0aGVpciBzY2hlZHVsZXNcbiAgICAvLyBNYXRjaCBzY2hlZHVsZXMgdXNpbmcgcHJvZmlsZV9pZCBzaW5jZSBkb2N0b3JfYXZhaWxhYmlsaXR5LmRvY3Rvcl9pZCByZWZlcmVuY2VzIHByb2ZpbGVzLmlkXG4gICAgY29uc3QgZG9jdG9ycyA9IGZpbHRlcmVkRG9jdG9yc0RhdGE/LmZpbHRlcihkb2N0b3IgPT4ge1xuICAgICAgY29uc3QgcHJvZmlsZUlkID0gZG9jdG9yLnByb2ZpbGVfaWQ7XG4gICAgICBjb25zdCBkb2N0b3JTY2hlZHVsZXMgPSBzY2hlZHVsZXM/LmZpbHRlcihzID0+IHMuZG9jdG9yX2lkID09PSBwcm9maWxlSWQpIHx8IFtdO1xuICAgICAgaWYgKGRvY3RvclNjaGVkdWxlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGRvY3Rvci5zY2hlZHVsZXMgPSBkb2N0b3JTY2hlZHVsZXM7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfVxuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH0pIHx8IFtdO1xuXG4gICAgY29uc29sZS5sb2coJ0RFQlVHOiBEb2N0b3JzIHdpdGggc2NoZWR1bGVzOicsIGRvY3RvcnMubGVuZ3RoKTtcblxuICAgIC8vIEdldCBzZXJ2aWNlIHByaWNpbmcgaWYgc2VydmljZUlkIGlzIHByb3ZpZGVkXG4gICAgbGV0IHNlcnZpY2VQcmljZSA9IG51bGw7XG4gICAgaWYgKHNlcnZpY2VJZCkge1xuICAgICAgY29uc3QgeyBkYXRhOiBzZXJ2aWNlRGF0YSwgZXJyb3I6IHNlcnZpY2VFcnJvciB9ID0gYXdhaXQgc2VydmljZVN1cGFiYXNlXG4gICAgICAgIC5mcm9tKCdzZXJ2aWNlcycpXG4gICAgICAgIC5zZWxlY3QoJ3ByaWNlJylcbiAgICAgICAgLmVxKCdpZCcsIHNlcnZpY2VJZClcbiAgICAgICAgLnNpbmdsZSgpO1xuXG4gICAgICBpZiAoIXNlcnZpY2VFcnJvciAmJiBzZXJ2aWNlRGF0YSkge1xuICAgICAgICBzZXJ2aWNlUHJpY2UgPSBzZXJ2aWNlRGF0YS5wcmljZTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBHZW5lcmF0ZSBhdmFpbGFibGUgdGltZSBzbG90cyB3aXRoIGRlZHVwbGljYXRpb25cbiAgICBjb25zdCBhdmFpbGFibGVTbG90czogVGltZVNsb3RbXSA9IFtdO1xuICAgIGNvbnN0IHNsb3RNYXAgPSBuZXcgTWFwPHN0cmluZywgVGltZVNsb3Q+KCk7IC8vIFVzZSBtYXAgdG8gZGVkdXBsaWNhdGUgYnkgdGltZSArIGRvY3RvclxuXG4gICAgZm9yIChjb25zdCBkb2N0b3Igb2YgZG9jdG9ycykge1xuICAgICAgY29uc3QgcHJvZmlsZUlkID0gZG9jdG9yLnByb2ZpbGVfaWQ7XG4gICAgICBjb25zdCBkb2N0b3JBcHBvaW50bWVudHMgPSBleGlzdGluZ0FwcG9pbnRtZW50cz8uZmlsdGVyKFxuICAgICAgICBhcHQgPT4gYXB0LmRvY3Rvcl9pZCA9PT0gcHJvZmlsZUlkXG4gICAgICApIHx8IFtdO1xuXG4gICAgICAvLyBHZXQgZG9jdG9yJ3Mgc2NoZWR1bGUgZm9yIHRoZSBkYXlcbiAgICAgIGNvbnN0IHNjaGVkdWxlcyA9IGRvY3Rvci5zY2hlZHVsZXMgfHwgW107XG5cbiAgICAgIC8vIE1lcmdlIG92ZXJsYXBwaW5nIHNjaGVkdWxlcyB0byBhdm9pZCBkdXBsaWNhdGVzXG4gICAgICBjb25zdCBtZXJnZWRTY2hlZHVsZXMgPSBtZXJnZVNjaGVkdWxlcyhzY2hlZHVsZXMpO1xuXG4gICAgICBmb3IgKGNvbnN0IHNjaGVkdWxlIG9mIG1lcmdlZFNjaGVkdWxlcykge1xuICAgICAgICBpZiAoc2NoZWR1bGUpIHtcbiAgICAgICAgICBjb25zdCBzbG90cyA9IGdlbmVyYXRlVGltZVNsb3RzKFxuICAgICAgICAgICAgc2NoZWR1bGUuc3RhcnRfdGltZSxcbiAgICAgICAgICAgIHNjaGVkdWxlLmVuZF90aW1lLFxuICAgICAgICAgICAgZHVyYXRpb24sXG4gICAgICAgICAgICBkb2N0b3JBcHBvaW50bWVudHNcbiAgICAgICAgICApO1xuXG4gICAgICAgICAgZm9yIChjb25zdCBzbG90IG9mIHNsb3RzKSB7XG4gICAgICAgICAgICBjb25zdCBwcm9maWxlID0gZG9jdG9yLnByb2ZpbGVzO1xuICAgICAgICAgICAgY29uc3Qgc2xvdEtleSA9IGAke2RvY3Rvci5pZH0tJHtzbG90LnN0YXJ0X3RpbWV9YDtcblxuICAgICAgICAgICAgLy8gT25seSBhZGQgaWYgbm90IGFscmVhZHkgZXhpc3RzIChwcmV2ZW50cyBkdXBsaWNhdGVzKVxuICAgICAgICAgICAgaWYgKCFzbG90TWFwLmhhcyhzbG90S2V5KSkge1xuICAgICAgICAgICAgICBjb25zdCB0aW1lU2xvdDogVGltZVNsb3QgPSB7XG4gICAgICAgICAgICAgICAgc3RhcnRfdGltZTogc2xvdC5zdGFydF90aW1lLFxuICAgICAgICAgICAgICAgIGVuZF90aW1lOiBzbG90LmVuZF90aW1lLFxuICAgICAgICAgICAgICAgIGRvY3Rvcl9pZDogZG9jdG9yLmlkLFxuICAgICAgICAgICAgICAgIGRvY3Rvcl9uYW1lOiBwcm9maWxlXG4gICAgICAgICAgICAgICAgICA/IGBEci4gJHtwcm9maWxlLmZpcnN0X25hbWV9ICR7cHJvZmlsZS5sYXN0X25hbWV9YFxuICAgICAgICAgICAgICAgICAgOiBgRHIuICR7ZG9jdG9yLnNwZWNpYWxpemF0aW9ufWAsXG4gICAgICAgICAgICAgICAgc3BlY2lhbGl6YXRpb246IGRvY3Rvci5zcGVjaWFsaXphdGlvbixcbiAgICAgICAgICAgICAgICBjb25zdWx0YXRpb25fZmVlOiBzZXJ2aWNlUHJpY2UgfHwgZG9jdG9yLmNvbnN1bHRhdGlvbl9mZWUsIC8vIFVzZSBzZXJ2aWNlIHByaWNlIGlmIGF2YWlsYWJsZVxuICAgICAgICAgICAgICAgIGF2YWlsYWJsZTogc2xvdC5hdmFpbGFibGVcbiAgICAgICAgICAgICAgfTtcblxuICAgICAgICAgICAgICBzbG90TWFwLnNldChzbG90S2V5LCB0aW1lU2xvdCk7XG4gICAgICAgICAgICAgIGF2YWlsYWJsZVNsb3RzLnB1c2godGltZVNsb3QpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFNvcnQgc2xvdHMgYnkgdGltZSBhbmQgZmlsdGVyIG9ubHkgYXZhaWxhYmxlIG9uZXNcbiAgICBjb25zdCBzb3J0ZWRBdmFpbGFibGVTbG90cyA9IGF2YWlsYWJsZVNsb3RzXG4gICAgICAuZmlsdGVyKHNsb3QgPT4gc2xvdC5hdmFpbGFibGUpXG4gICAgICAuc29ydCgoYSwgYikgPT4gYS5zdGFydF90aW1lLmxvY2FsZUNvbXBhcmUoYi5zdGFydF90aW1lKSk7XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIGRhdGE6IHNvcnRlZEF2YWlsYWJsZVNsb3RzLFxuICAgICAgY291bnQ6IHNvcnRlZEF2YWlsYWJsZVNsb3RzLmxlbmd0aCxcbiAgICAgIGRhdGU6IGRhdGUsXG4gICAgICBkYXlfb2Zfd2VlazogZGF5T2ZXZWVrLFxuICAgICAgZHVyYXRpb25fbWludXRlczogZHVyYXRpb25cbiAgICB9KTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1VuZXhwZWN0ZWQgZXJyb3IgaW4gR0VUIC9hcGkvZG9jdG9ycy9hdmFpbGFiaWxpdHk6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG5cbi8qKlxuICogR2VuZXJhdGUgdGltZSBzbG90cyBmb3IgYSBnaXZlbiB0aW1lIHJhbmdlXG4gKi9cbmZ1bmN0aW9uIGdlbmVyYXRlVGltZVNsb3RzKFxuICBzdGFydFRpbWU6IHN0cmluZyxcbiAgZW5kVGltZTogc3RyaW5nLFxuICBkdXJhdGlvbk1pbnV0ZXM6IG51bWJlcixcbiAgZXhpc3RpbmdBcHBvaW50bWVudHM6IEFycmF5PHsgc3RhcnRfdGltZTogc3RyaW5nOyBlbmRfdGltZTogc3RyaW5nIH0+XG4pOiBBcnJheTx7IHN0YXJ0X3RpbWU6IHN0cmluZzsgZW5kX3RpbWU6IHN0cmluZzsgYXZhaWxhYmxlOiBib29sZWFuIH0+IHtcbiAgY29uc3Qgc2xvdHM6IEFycmF5PHsgc3RhcnRfdGltZTogc3RyaW5nOyBlbmRfdGltZTogc3RyaW5nOyBhdmFpbGFibGU6IGJvb2xlYW4gfT4gPSBbXTtcblxuICAvLyBDb252ZXJ0IHRpbWUgc3RyaW5ncyB0byBtaW51dGVzIHNpbmNlIG1pZG5pZ2h0XG4gIGNvbnN0IHN0YXJ0TWludXRlcyA9IHRpbWVUb01pbnV0ZXMoc3RhcnRUaW1lKTtcbiAgY29uc3QgZW5kTWludXRlcyA9IHRpbWVUb01pbnV0ZXMoZW5kVGltZSk7XG5cbiAgLy8gR2VuZXJhdGUgc2xvdHNcbiAgZm9yIChsZXQgY3VycmVudE1pbnV0ZXMgPSBzdGFydE1pbnV0ZXM7IGN1cnJlbnRNaW51dGVzICsgZHVyYXRpb25NaW51dGVzIDw9IGVuZE1pbnV0ZXM7IGN1cnJlbnRNaW51dGVzICs9IGR1cmF0aW9uTWludXRlcykge1xuICAgIGNvbnN0IHNsb3RTdGFydCA9IG1pbnV0ZXNUb1RpbWUoY3VycmVudE1pbnV0ZXMpO1xuICAgIGNvbnN0IHNsb3RFbmQgPSBtaW51dGVzVG9UaW1lKGN1cnJlbnRNaW51dGVzICsgZHVyYXRpb25NaW51dGVzKTtcblxuICAgIC8vIENoZWNrIGlmIHNsb3QgY29uZmxpY3RzIHdpdGggZXhpc3RpbmcgYXBwb2ludG1lbnRzXG4gICAgY29uc3QgaGFzQ29uZmxpY3QgPSBleGlzdGluZ0FwcG9pbnRtZW50cy5zb21lKGFwcG9pbnRtZW50ID0+IHtcbiAgICAgIGNvbnN0IGFwdFN0YXJ0ID0gdGltZVRvTWludXRlcyhhcHBvaW50bWVudC5zdGFydF90aW1lKTtcbiAgICAgIGNvbnN0IGFwdEVuZCA9IHRpbWVUb01pbnV0ZXMoYXBwb2ludG1lbnQuZW5kX3RpbWUpO1xuXG4gICAgICByZXR1cm4gKFxuICAgICAgICAoY3VycmVudE1pbnV0ZXMgPj0gYXB0U3RhcnQgJiYgY3VycmVudE1pbnV0ZXMgPCBhcHRFbmQpIHx8XG4gICAgICAgIChjdXJyZW50TWludXRlcyArIGR1cmF0aW9uTWludXRlcyA+IGFwdFN0YXJ0ICYmIGN1cnJlbnRNaW51dGVzICsgZHVyYXRpb25NaW51dGVzIDw9IGFwdEVuZCkgfHxcbiAgICAgICAgKGN1cnJlbnRNaW51dGVzIDw9IGFwdFN0YXJ0ICYmIGN1cnJlbnRNaW51dGVzICsgZHVyYXRpb25NaW51dGVzID49IGFwdEVuZClcbiAgICAgICk7XG4gICAgfSk7XG5cbiAgICBzbG90cy5wdXNoKHtcbiAgICAgIHN0YXJ0X3RpbWU6IHNsb3RTdGFydCxcbiAgICAgIGVuZF90aW1lOiBzbG90RW5kLFxuICAgICAgYXZhaWxhYmxlOiAhaGFzQ29uZmxpY3RcbiAgICB9KTtcbiAgfVxuXG4gIHJldHVybiBzbG90cztcbn1cblxuLyoqXG4gKiBDb252ZXJ0IHRpbWUgc3RyaW5nIChISDpNTSkgdG8gbWludXRlcyBzaW5jZSBtaWRuaWdodFxuICovXG5mdW5jdGlvbiB0aW1lVG9NaW51dGVzKHRpbWVTdHJpbmc6IHN0cmluZyk6IG51bWJlciB7XG4gIGNvbnN0IHBhcnRzID0gdGltZVN0cmluZy5zcGxpdCgnOicpLm1hcChOdW1iZXIpO1xuICBjb25zdCBob3VycyA9IHBhcnRzWzBdIHx8IDA7XG4gIGNvbnN0IG1pbnV0ZXMgPSBwYXJ0c1sxXSB8fCAwO1xuICByZXR1cm4gaG91cnMgKiA2MCArIG1pbnV0ZXM7XG59XG5cbi8qKlxuICogQ29udmVydCBtaW51dGVzIHNpbmNlIG1pZG5pZ2h0IHRvIHRpbWUgc3RyaW5nIChISDpNTSlcbiAqL1xuZnVuY3Rpb24gbWludXRlc1RvVGltZShtaW51dGVzOiBudW1iZXIpOiBzdHJpbmcge1xuICBjb25zdCBob3VycyA9IE1hdGguZmxvb3IobWludXRlcyAvIDYwKTtcbiAgY29uc3QgbWlucyA9IG1pbnV0ZXMgJSA2MDtcbiAgcmV0dXJuIGAke2hvdXJzLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKX06JHttaW5zLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKX1gO1xufVxuXG4vKipcbiAqIE1lcmdlIG92ZXJsYXBwaW5nIHNjaGVkdWxlcyB0byBwcmV2ZW50IGR1cGxpY2F0ZSB0aW1lIHNsb3RzXG4gKi9cbmZ1bmN0aW9uIG1lcmdlU2NoZWR1bGVzKHNjaGVkdWxlczogQXJyYXk8eyBzdGFydF90aW1lOiBzdHJpbmc7IGVuZF90aW1lOiBzdHJpbmcgfT4pOiBBcnJheTx7IHN0YXJ0X3RpbWU6IHN0cmluZzsgZW5kX3RpbWU6IHN0cmluZyB9PiB7XG4gIGlmIChzY2hlZHVsZXMubGVuZ3RoIDw9IDEpIHJldHVybiBzY2hlZHVsZXM7XG5cbiAgLy8gU29ydCBzY2hlZHVsZXMgYnkgc3RhcnQgdGltZVxuICBjb25zdCBzb3J0ZWQgPSBzY2hlZHVsZXNcbiAgICAubWFwKHMgPT4gKHtcbiAgICAgIHN0YXJ0X3RpbWU6IHMuc3RhcnRfdGltZSxcbiAgICAgIGVuZF90aW1lOiBzLmVuZF90aW1lLFxuICAgICAgc3RhcnRNaW51dGVzOiB0aW1lVG9NaW51dGVzKHMuc3RhcnRfdGltZSksXG4gICAgICBlbmRNaW51dGVzOiB0aW1lVG9NaW51dGVzKHMuZW5kX3RpbWUpXG4gICAgfSkpXG4gICAgLnNvcnQoKGEsIGIpID0+IGEuc3RhcnRNaW51dGVzIC0gYi5zdGFydE1pbnV0ZXMpO1xuXG4gIGNvbnN0IG1lcmdlZDogQXJyYXk8eyBzdGFydF90aW1lOiBzdHJpbmc7IGVuZF90aW1lOiBzdHJpbmcgfT4gPSBbXTtcbiAgbGV0IGN1cnJlbnQgPSBzb3J0ZWRbMF07XG5cbiAgZm9yIChsZXQgaSA9IDE7IGkgPCBzb3J0ZWQubGVuZ3RoOyBpKyspIHtcbiAgICBjb25zdCBuZXh0ID0gc29ydGVkW2ldO1xuXG4gICAgLy8gSWYgY3VycmVudCBzY2hlZHVsZSBvdmVybGFwcyBvciBpcyBhZGphY2VudCB0byBuZXh0LCBtZXJnZSB0aGVtXG4gICAgaWYgKGN1cnJlbnQuZW5kTWludXRlcyA+PSBuZXh0LnN0YXJ0TWludXRlcykge1xuICAgICAgY3VycmVudCA9IHtcbiAgICAgICAgc3RhcnRfdGltZTogY3VycmVudC5zdGFydF90aW1lLFxuICAgICAgICBlbmRfdGltZTogY3VycmVudC5lbmRNaW51dGVzID49IG5leHQuZW5kTWludXRlcyA/IGN1cnJlbnQuZW5kX3RpbWUgOiBuZXh0LmVuZF90aW1lLFxuICAgICAgICBzdGFydE1pbnV0ZXM6IGN1cnJlbnQuc3RhcnRNaW51dGVzLFxuICAgICAgICBlbmRNaW51dGVzOiBNYXRoLm1heChjdXJyZW50LmVuZE1pbnV0ZXMsIG5leHQuZW5kTWludXRlcylcbiAgICAgIH07XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIE5vIG92ZXJsYXAsIGFkZCBjdXJyZW50IHRvIG1lcmdlZCBhbmQgbW92ZSB0byBuZXh0XG4gICAgICBtZXJnZWQucHVzaCh7XG4gICAgICAgIHN0YXJ0X3RpbWU6IGN1cnJlbnQuc3RhcnRfdGltZSxcbiAgICAgICAgZW5kX3RpbWU6IGN1cnJlbnQuZW5kX3RpbWVcbiAgICAgIH0pO1xuICAgICAgY3VycmVudCA9IG5leHQ7XG4gICAgfVxuICB9XG5cbiAgLy8gQWRkIHRoZSBsYXN0IHNjaGVkdWxlXG4gIG1lcmdlZC5wdXNoKHtcbiAgICBzdGFydF90aW1lOiBjdXJyZW50LnN0YXJ0X3RpbWUsXG4gICAgZW5kX3RpbWU6IGN1cnJlbnQuZW5kX3RpbWVcbiAgfSk7XG5cbiAgcmV0dXJuIG1lcmdlZDtcbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJjcmVhdGVDbGllbnQiLCJ6IiwiYXZhaWxhYmlsaXR5UXVlcnlTY2hlbWEiLCJvYmplY3QiLCJvcmdhbml6YXRpb25JZCIsInN0cmluZyIsInV1aWQiLCJzZXJ2aWNlSWQiLCJvcHRpb25hbCIsImRvY3RvcklkIiwiZGF0ZSIsInJlZ2V4IiwiZHVyYXRpb24iLCJ0cmFuc2Zvcm0iLCJOdW1iZXIiLCJwaXBlIiwibnVtYmVyIiwibWluIiwibWF4IiwiZGVmYXVsdCIsIkdFVCIsInJlcXVlc3QiLCJzdXBhYmFzZSIsInNlYXJjaFBhcmFtcyIsIlVSTCIsInVybCIsInF1ZXJ5UGFyYW1zIiwiZ2V0IiwidW5kZWZpbmVkIiwidmFsaWRhdGlvblJlc3VsdCIsInNhZmVQYXJzZSIsInN1Y2Nlc3MiLCJjb25zb2xlIiwiZXJyb3IiLCJlcnJvcnMiLCJqc29uIiwiZGV0YWlscyIsInJlY2VpdmVkIiwic3RhdHVzIiwiZGF0YSIsInRhcmdldERhdGUiLCJEYXRlIiwiZGF5T2ZXZWVrIiwiZ2V0RGF5IiwibG9nIiwidXNlciIsImF1dGhFcnJvciIsImF1dGgiLCJnZXRVc2VyIiwiaWQiLCJtZXNzYWdlIiwiY3JlYXRlU2VydmljZUNsaWVudCIsInNlcnZpY2VTdXBhYmFzZSIsImFsbERvY3RvcnMiLCJkb2N0b3JTZXJ2aWNlcyIsInNlcnZpY2VFcnJvciIsImZyb20iLCJzZWxlY3QiLCJlcSIsImRvY3RvcklkcyIsIm1hcCIsImRzIiwiZG9jdG9yX2lkIiwibGVuZ3RoIiwic2xvdHMiLCJmaWx0ZXJlZERvY3RvcnMiLCJkb2N0b3JzRXJyb3IiLCJpbiIsImFsbERvY3RvcnNEYXRhIiwiZG9jdG9yc0RhdGEiLCJmaWx0ZXJlZERvY3RvcnNEYXRhIiwiZmlsdGVyIiwiZG9jdG9yIiwicHJvZmlsZUlkcyIsImQiLCJwcm9maWxlX2lkIiwiQm9vbGVhbiIsInNjaGVkdWxlc1Jlc3VsdCIsImFwcG9pbnRtZW50c1Jlc3VsdCIsIlByb21pc2UiLCJhbGwiLCJzY2hlZHVsZXMiLCJzY2hlZHVsZXNFcnJvciIsImV4aXN0aW5nQXBwb2ludG1lbnRzIiwiYXBwb2ludG1lbnRzRXJyb3IiLCJkb2N0b3JzIiwicHJvZmlsZUlkIiwiZG9jdG9yU2NoZWR1bGVzIiwicyIsInNlcnZpY2VQcmljZSIsInNlcnZpY2VEYXRhIiwic2luZ2xlIiwicHJpY2UiLCJhdmFpbGFibGVTbG90cyIsInNsb3RNYXAiLCJNYXAiLCJkb2N0b3JBcHBvaW50bWVudHMiLCJhcHQiLCJtZXJnZWRTY2hlZHVsZXMiLCJtZXJnZVNjaGVkdWxlcyIsInNjaGVkdWxlIiwiZ2VuZXJhdGVUaW1lU2xvdHMiLCJzdGFydF90aW1lIiwiZW5kX3RpbWUiLCJzbG90IiwicHJvZmlsZSIsInByb2ZpbGVzIiwic2xvdEtleSIsImhhcyIsInRpbWVTbG90IiwiZG9jdG9yX25hbWUiLCJmaXJzdF9uYW1lIiwibGFzdF9uYW1lIiwic3BlY2lhbGl6YXRpb24iLCJjb25zdWx0YXRpb25fZmVlIiwiYXZhaWxhYmxlIiwic2V0IiwicHVzaCIsInNvcnRlZEF2YWlsYWJsZVNsb3RzIiwic29ydCIsImEiLCJiIiwibG9jYWxlQ29tcGFyZSIsImNvdW50IiwiZGF5X29mX3dlZWsiLCJkdXJhdGlvbl9taW51dGVzIiwic3RhcnRUaW1lIiwiZW5kVGltZSIsImR1cmF0aW9uTWludXRlcyIsInN0YXJ0TWludXRlcyIsInRpbWVUb01pbnV0ZXMiLCJlbmRNaW51dGVzIiwiY3VycmVudE1pbnV0ZXMiLCJzbG90U3RhcnQiLCJtaW51dGVzVG9UaW1lIiwic2xvdEVuZCIsImhhc0NvbmZsaWN0Iiwic29tZSIsImFwcG9pbnRtZW50IiwiYXB0U3RhcnQiLCJhcHRFbmQiLCJ0aW1lU3RyaW5nIiwicGFydHMiLCJzcGxpdCIsImhvdXJzIiwibWludXRlcyIsIk1hdGgiLCJmbG9vciIsIm1pbnMiLCJ0b1N0cmluZyIsInBhZFN0YXJ0Iiwic29ydGVkIiwibWVyZ2VkIiwiY3VycmVudCIsImkiLCJuZXh0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/doctors/availability/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://fjvletqwwmxusgthwphr.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZqdmxldHF3d214dXNndGh3cGhyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDc2MDAsImV4cCI6MjA2Mzc4MzYwMH0.TiU8DGo9kihikfmlk1drLs57tNuOrm_Pgq80yzsWytc\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set(name, value, options);\n                } catch  {\n                // The `set` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            },\n            remove (name, options) {\n                try {\n                    cookieStore.set(name, \"\", {\n                        ...options,\n                        maxAge: 0\n                    });\n                } catch  {\n                // The `remove` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/@opentelemetry","vendor-chunks/ramda","vendor-chunks/cookie","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdoctors%2Favailability%2Froute&page=%2Fapi%2Fdoctors%2Favailability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdoctors%2Favailability%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cagensalud-sonnet4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();